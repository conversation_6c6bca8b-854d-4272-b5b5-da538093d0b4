import net from "net";
import { v4 as uuidv4 } from "uuid";

/**
 * TCP消息接口
 */
export interface TcpMessage {
  messageType: string;
  data: any;
  messageId: string;
}

/**
 * TCP消息处理器类型
 */
export type TcpMessageHandler = (message: TcpMessage) => any;

/**
 * TCP通信类
 * 基于参考代码实现，用于处理TCP连接和消息传递
 */
export class TcpMessenger {
  private port: number = 3000;
  private host: string = "127.0.0.1";
  private socket: net.Socket | null = null;
  private server: net.Server | null = null;

  /** 消息类型监听器映射 */
  private typeListeners = new Map<string, TcpMessageHandler[]>();
  /** 消息ID监听器映射 */
  private idListeners = new Map<string, TcpMessageHandler>();
  /** 错误处理器数组 */
  private _onErrorHandlers: ((error: Error) => void)[] = [];

  constructor(port?: number, host?: string) {
    if (port) this.port = port;
    if (host) this.host = host;
    
    this.setupServer();
  }

  /**
   * 设置TCP服务器
   */
  private setupServer() {
    this.server = net.createServer((socket) => {
      console.log(`[TCP] Client connected from ${socket.remoteAddress}:${socket.remotePort}`);
      this.socket = socket;

      socket.on("connect", () => {
        console.log("[TCP] Connected to server");
      });

      socket.on("data", (data: Buffer) => {
        this._handleData(data);
      });

      socket.on("end", () => {
        console.log("[TCP] Client disconnected");
        this.socket = null;
      });

      socket.on("error", (err: any) => {
        console.error("[TCP] Client error:", err);
        this._onErrorHandlers.forEach((handler) => {
          handler(err);
        });
      });
    });

    this.server.listen(this.port, this.host, () => {
      console.log(`[TCP] Server listening on ${this.host}:${this.port}`);
    });

    this.server.on("error", (err) => {
      console.error("[TCP] Server error:", err);
      this._onErrorHandlers.forEach((handler) => {
        handler(err);
      });
    });
  }

  /**
   * 添加错误处理器
   */
  onError(handler: (error: Error) => void) {
    this._onErrorHandlers.push(handler);
  }

  /**
   * 等待客户端连接
   */
  public async awaitConnection(): Promise<void> {
    while (!this.socket) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  /**
   * 处理接收到的数据行
   */
  private _handleLine(line: string) {
    try {
      const msg: TcpMessage = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error("Invalid message sent: " + JSON.stringify(msg));
      }

      // 调用类型监听器并响应返回值
      const listeners = this.typeListeners.get(msg.messageType);
      listeners?.forEach(async (handler) => {
        try {
          const response = await handler(msg);
          if (
            response &&
            typeof response[Symbol.asyncIterator] === "function"
          ) {
            // 处理异步迭代器响应
            for await (const update of response) {
              this.send(msg.messageType, update, msg.messageId);
            }
            this.send(msg.messageType, { done: true }, msg.messageId);
          } else {
            this.send(msg.messageType, response || {}, msg.messageId);
          }
        } catch (e: any) {
          console.warn(`[TCP] Error running handler for "${msg.messageType}": `, e);
          this._onErrorHandlers.forEach((handler) => {
            handler(e);
          });
        }
      });

      // 调用等待响应的处理器
      this.idListeners.get(msg.messageId)?.(msg);
    } catch (e) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine =
          line.substring(0, 100) + "..." + line.substring(line.length - 100);
      }
      console.error("[TCP] Error parsing line: ", truncatedLine, e);
      return;
    }
  }

  private _unfinishedLine: string | undefined = undefined;
  
  /**
   * 处理接收到的数据
   */
  private _handleData(data: Buffer) {
    const d = data.toString();
    const lines = d.split(/\r\n/).filter((line) => line.trim() !== "");
    if (lines.length === 0) {
      return;
    }

    if (this._unfinishedLine) {
      lines[0] = this._unfinishedLine + lines[0];
      this._unfinishedLine = undefined;
    }
    if (!d.endsWith("\r\n")) {
      this._unfinishedLine = lines.pop();
    }
    lines.forEach((line) => this._handleLine(line));
  }

  /**
   * 发送消息
   */
  send(messageType: string, data: any, messageId?: string): string {
    messageId = messageId ?? uuidv4();
    const msg: TcpMessage = {
      messageType,
      data,
      messageId,
    };

    if (this.socket) {
      this.socket.write(JSON.stringify(msg) + "\r\n");
    } else {
      console.warn("[TCP] No client connected, message not sent:", msg);
    }
    return messageId;
  }

  /**
   * 注册消息类型监听器
   */
  on(messageType: string, handler: TcpMessageHandler): void {
    if (!this.typeListeners.has(messageType)) {
      this.typeListeners.set(messageType, []);
    }
    this.typeListeners.get(messageType)?.push(handler);
  }

  /**
   * 调用本地处理器
   */
  invoke(messageType: string, data: any): any {
    return this.typeListeners.get(messageType)?.[0]?.({
      messageId: uuidv4(),
      messageType,
      data,
    });
  }

  /**
   * 发送请求并等待响应
   */
  request(messageType: string, data: any): Promise<any> {
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: TcpMessage) => {
        resolve(msg.data);
        this.idListeners.delete(messageId);
      };
      this.idListeners.set(messageId, handler);
      this.send(messageType, data, messageId);
    });
  }

  /**
   * 关闭TCP连接和服务器
   */
  close(): void {
    if (this.socket) {
      this.socket.end();
      this.socket = null;
    }
    if (this.server) {
      this.server.close();
      this.server = null;
    }
  }
}
