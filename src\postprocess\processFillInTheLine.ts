import { PostprocessFilter } from "./base";
import { splitLines } from "../utils";
import { CompletionContext } from "../CompletionContext";

/**
 * 创建一个处理行内补全的过滤器
 * 处理单行补全时的重叠内容，避免重复
 * 
 * @returns 处理行内补全的过滤器函数
 */
export function processFillInTheLine(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const {currentLineSuffix } = context;
    const inputLines = splitLines(input);
    
    // 检查是否为单行输入
    const isSingle = inputLines.length == 1 || (inputLines.length == 2 && inputLines[1]!.trim() == "")
    
    // 快速路径：多行内容或空后缀直接返回
    if (!isSingle || currentLineSuffix.trim() == "" || input.trim().length < currentLineSuffix.trim().length) {
      return input;
    }
    
    // 输入验证
    if (inputLines.length < 1 || input.trim().length < currentLineSuffix.trim().length) {
      return input;
    }
    
    const trimmedSuffix = currentLineSuffix.trim();
    let trimmedInput = inputLines[0]!.trim();

    // 从后向前比较字符，找到共同的结尾部分
    let overlappingLength = 0;
    for (let i=0; i < trimmedSuffix.length; i++) {
      if (trimmedSuffix.charAt(trimmedSuffix.length-1-i) == trimmedInput.charAt(trimmedInput.length-1-i)) {
        overlappingLength+=1;
      }
      else {
        break;
      }
    }

    // 修剪共同的结尾部分
    trimmedInput = overlappingLength == 0 ? trimmedInput : trimmedInput.slice(0, -overlappingLength);
    return trimmedInput;
  }
}