import { CompletionContext } from "../CompletionContext";
import { AgentConfig } from "../AgentConfig";
import { isBrowser } from "../env";
import { PostprocessFilter } from "./base";
import { limitScopeByIndentation } from "./limitScopeByIndentation";
import { limitScopeBySyntax, supportedLanguages } from "./limitScopeBySyntax";

/**
 * 创建一个限制补全范围的过滤器
 * 根据环境和配置选择合适的范围限制方法（基于缩进或语法分析）
 * 
 * @param config - 范围限制相关的配置
 * @returns 限制补全范围的过滤器函数
 */
export function limitScope(config: AgentConfig["postprocess"]["limitScope"]): PostprocessFilter {
  return isBrowser
    ? (input: string, context: CompletionContext) => {
        // 浏览器环境暂不支持语法分析器，使用基于缩进的方法
        return limitScopeByIndentation(config["indentation"])(input, context);
      }
    : (input: string, context: CompletionContext) => {
        // 根据配置和语言支持选择范围限制方法
        if (config.experimentalSyntax && supportedLanguages.includes(context.language)) {
          return limitScopeBySyntax()(input, context);
        } else {
          return limitScopeByIndentation(config["indentation"])(input, context);
        }
      };
}
