{"version": "0.2.0", "configurations": [{"name": "Debug Tabby Agent (TCP Mode) - new", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "args": ["--tcp", "--port", "1983", "--host", "0.0.0.0", "--server-type", "codefree", "--server-base-url", "https://**************:3443/oscap"], "env": {"NODE_ENV": "development", "TABBY_DEVELOPMENT": "true", "TABBY_AGENT_TCP_MODE": "true", "TABBY_AGENT_TCP_PORT": "1983", "TABBY_AGENT_TCP_HOST": "0.0.0.0", "FORCE_COLOR": "1"}, "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "sourceMapPathOverrides": {"../src/*": "${workspaceFolder}/src/*", "../node_modules/*": "${workspaceFolder}/node_modules/*"}, "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"]}, {"name": "Debug Tabby Agent (TCP Mode)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/cli.ts", "args": ["--tcp", "--port", "1983", "--server-type", "codefree", "--server-base-url", "https://**************:3443/oscap"], "env": {"NODE_ENV": "development", "TABBY_DEVELOPMENT": "true", "TABBY_AGENT_TCP_MODE": "true", "TABBY_AGENT_TCP_PORT": "3983", "TABBY_AGENT_TCP_HOST": "127.0.0.1"}, "runtimeArgs": ["-r", "ts-node/register"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"], "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "restart": true, "presentation": {"hidden": false, "group": "tabby-agent", "order": 1}}]}