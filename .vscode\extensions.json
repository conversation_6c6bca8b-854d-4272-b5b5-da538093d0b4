{"recommendations": ["ms-vscode.vscode-typescript-next", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-node-azure-pack", "ms-vscode.remote-containers", "ms-vscode.remote-ssh", "ms-vscode.remote-wsl", "github.copilot", "github.copilot-chat", "ms-vscode.hexeditor", "redhat.vscode-yaml", "ms-vscode.powershell", "ms-vscode.vscode-markdown"], "unwantedRecommendations": ["ms-vscode.vscode-typescript"]}