import { PostprocessFilter } from "./base";
import { splitLines, isLineIncomplete, joinLines } from "../utils";

/**
 * 创建一个过滤器，移除未完成的多行补全项的最后一行
 * 
 * 检查多行补全内容的最后一行是否未完成（例如，缺少闭合括号或分号），
 * 如果未完成则移除最后一行
 * 
 * @returns 过滤未完成多行补全的过滤器函数
 */
export function dropUnfinishedMulti(): PostprocessFilter {
  return (input: string) => {
    // 分割输入为行数组
    const inputLines = splitLines(input);
    
    // 单行内容不需要处理
    if (inputLines.length <= 1) {
      return input;
    }
    
    // 检查最后一行是否未完成
    if (isLineIncomplete(inputLines[inputLines.length-1]!, inputLines[inputLines.length-2]!)) {
      // 移除最后一行
      inputLines.pop();
      // 重新连接剩余行
      return joinLines(inputLines);
    }
    else {
      // 保持原样
      return input;
    }
  };
}
