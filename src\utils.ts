interface LanguageConfig {
  blockStart: string       // 代码块开始标记，如 '{', 'do', ':' 等
  blockElseTest: RegExp    // 用于测试是否是 else 语句的正则表达式
  blockEnd: string | null  // 代码块结束标记，某些语言如 Python 可能为 null
  commentStart: string     // 单行注释起始字符
}
// Constants for configuration
export const MAX_LINE_LENGTH = 200; // Threshold for max line length
export const MIN_PREFIX_CHARS = 3; // Minimum chars required in prefix after trimming
export const IMPORT_KEYWORDS = {
  typescript: ['import'],
  javascript: ['import', 'require'],
  python: ['import', 'from'],
  java: ['import', 'package'],
  go: ['import', 'package'],
  rust: ['use', 'extern', 'crate'],
  php: ['use', 'require', 'require_once', 'include', 'include_once'],
};

// Common import/package patterns for different languages
export const IMPORT_PATTERNS = {
  typescript: /^import\s.+/,
  javascript: /^(import\s.+|require\(.+\))/,
  python: /^(import\s.+|from\s.+\simport\s.+)/,
  java: /^(import\s.+|package\s.+)/,
  go: /^(import\s.*|package\s.+)/,
  rust: /^(use\s.+|extern\s+crate\s.+)/,
  php: /^(use\s.+|require(_once)?\s.+|include(_once)?\s.+)/,
};
/**
 * 检查给定行是否为导入或包声明语句
 * @param line - 要检查的代码行
 * @param language - 编程语言标识符
 * @param previousLines - 当前行之前的代码行数组，用于上下文分析
 * @returns 是否为导入/包声明语句
 */
export function isImportOrPackageLine(line: string, language: string, previousLines: string[] = []): boolean {
  const trimmedLine = line.trim();
  
  // First check full pattern match
  const pattern = IMPORT_PATTERNS[language as keyof typeof IMPORT_PATTERNS];
  if (pattern?.test(trimmedLine)) {
    return true;
  }

  // If any previous line is an import statement, check if current line starts with 
  // a prefix of any import keyword
  const keywords = IMPORT_KEYWORDS[language as keyof typeof IMPORT_KEYWORDS] || [];
  const hasPreviousImports = previousLines.some(prevLine => 
    pattern?.test(prevLine.trim())
  );

  if (hasPreviousImports) {
    return keywords.some(keyword => 
      keyword.toLowerCase().startsWith(trimmedLine.toLowerCase()) || 
      trimmedLine.toLowerCase().startsWith(keyword.toLowerCase())
    );
  }

  return false;
}

/**
 * 检查给定行是否为注释
 * @param line - 要检查的代码行
 * @param language - 编程语言标识符
 * @returns 是否为注释行
 */
export function isCommentLine(line: string, language: string): boolean {
  const config = getLanguageConfig(language);
  if (!config) return false;

  const trimmedLine = line.trim();
  return trimmedLine.startsWith(config.commentStart) || 
         // Handle multi-line comments for C-style languages
         (trimmedLine.startsWith('/*') || trimmedLine.includes('*/') || trimmedLine.startsWith('//') || trimmedLine.startsWith('#'));
}

/**
 * 检查字符是否为字母、数字或下划线
 * @param char - 要检查的字符
 * @returns 是否为字母数字字符
 */
export function isAlphanumeric(char: string): boolean {
  return /^[a-zA-Z0-9_]$/.test(char);
}


/**
 * 获取特定语言的配置信息
 * @param languageId - 语言标识符
 * @returns 语言配置对象或 null
 */
export function getLanguageConfig(languageId: string): LanguageConfig | null {
  switch (languageId) {
    case 'astro':
    case 'c':
    case 'cpp':
    case 'csharp':
    case 'dart':
    case 'go':
    case 'java':
    case 'javascript':
    case 'javascriptreact':
    case 'kotlin':
    case 'php':
    case 'rust':
    case 'svelte':
    case 'typescript':
    case 'typescriptreact':
    case 'vue':
      return {
        blockStart: '{',
        blockElseTest: /^[\t ]*} else/,
        blockEnd: '}',
        commentStart: '// ',
      }
    case 'python': {
      return {
        blockStart: ':',
        blockElseTest: /^[\t ]*(elif |else:)/,
        blockEnd: null,
        commentStart: '# ',
      }
    }
    case 'elixir': {
      return {
        blockStart: 'do',
        blockElseTest: /^[\t ]*(else|else do)/,
        blockEnd: 'end',
        commentStart: '#',
      }
    }
    default:
      return null
  }
}

/**
 * 将输入字符串按行分割，处理各种换行符情况
 * @param input - 要分割的输入字符串
 * @returns 分割后的行数组
 */
export function splitLines(input: string): string[] {
  if (!input) return [];

  // Single pass normalization using a more efficient regex
  const normalizedInput = input.replace(/\\[rn]|\\r\\n/g, match => {
    switch (match) {
      case '\\r\\n': return '\r\n';
      case '\\r': return '\r';
      case '\\n': return '\n';
      default: return match;
    }
  });

  // Use array to build result instead of intermediate string
  const lines: string[] = [];
  let currentLine = '';
  let i = 0;

  while (i < normalizedInput.length) {
    const char = normalizedInput[i];
    const nextChar = normalizedInput[i + 1];

    if (char === '\r' && nextChar === '\n') {
      // Handle CRLF
      lines.push(currentLine + '\n');
      currentLine = '';
      i += 2;
    } else if (char === '\r' || char === '\n') {
      // Handle CR or LF
      lines.push(currentLine + '\n');
      currentLine = '';
      i++;
    } else {
      currentLine += char;
      i++;
    }
  }

  // Handle the last line
  lines.push(currentLine);

  return lines;
}
/**
 * 将行数组合并为单个字符串
 * @param lines - 要合并的行数组
 * @returns 合并后的字符串
 */
export function joinLines(lines: string[]): string {
  // Join lines with newline characters
  return lines.join("");
}
/**
 * 将输入字符串分割为单词和非单词部分
 * @param input - 要分割的输入字符串
 * @returns 分割后的单词数组
 */
export function splitWords(input: string) {
  return input.match(/\w+|\W+/g)?.filter(Boolean) ?? []; // Split consecutive words and non-words
}

/**
 * 检查字符串是否为空白
 * @param input - 要检查的字符串
 * @returns 是否为空白字符串
 */
export function isBlank(input: string) {
  return input.trim().length === 0;
}
/**
 * 检查代码行是否不完整（需要继续）
 * @param line - 当前行
 * @param previousLine - 前一行
 * @returns 是否为不完整行
 */
export function isLineIncomplete(line: string, previousLine: string): boolean {
  line = line.trim();
  previousLine = previousLine.trim();
  // Count of opening and closing brackets
  const openingBrackets = (line.match(/\(/g) || []).length;
  const closingBrackets = (line.match(/\)/g) || []).length;
  const openingCurlyBraces = (line.match(/{/g) || []).length;
  const closingCurlyBraces = (line.match(/}/g) || []).length;
  const openingSquareBrackets = (line.match(/\[/g) || []).length;
  const closingSquareBrackets = (line.match(/\]/g) || []).length;
  const openingAngleBrackets = (line.match(/</g) || []).length;
  const closingAngleBrackets = (line.match(/>/g) || []).length;

  // Check if opening brackets are more than closing brackets
  const unclosedBrackets = openingBrackets > closingBrackets;
  const unclosedCurlyBraces = openingCurlyBraces > closingCurlyBraces;
  const unclosedSquareBrackets = openingSquareBrackets > closingSquareBrackets;
  const unclosedAngleBrackets = openingAngleBrackets > closingAngleBrackets;

  // Check for patterns that may indicate incomplete syntax
  const unfinishedComma = /,$/.test(line) && !/,$/.test(previousLine);
  const unfinishedColon = /:$/.test(line);
  const unfinishedDot = /\.$/.test(line);

  const unfinishedEqual = /=$/.test(line);
  const unfinishedString = (line.match(/"/g)?.length || 0) % 2 !== 0 ||
    (line.match(/'/g)?.length || 0) % 2 !== 0 ||
    (line.match(/`/g)?.length || 0) % 2 !== 0;
  
  return unclosedBrackets || unclosedCurlyBraces || unclosedSquareBrackets || unclosedAngleBrackets ||
    unfinishedComma || unfinishedColon || unfinishedEqual || unfinishedString  || unfinishedDot || (previousLine.startsWith(line) && previousLine.length > line.length * 2);
}


export const autoClosingPairs = [
  ["(", ")"],
  ["[", "]"],
  ["{", "}"],
  ["'", "'"],
  ['"', '"'],
  ["`", "`"],
];

export const autoClosingPairOpenings = autoClosingPairs.map((pair) => pair[0]);
export const autoClosingPairClosings = autoClosingPairs.map((pair) => pair[1]);
export const FUNCTION_OR_METHOD_INVOCATION_REGEX = /\b[^()]+\((.*)\)$/g // Match function calls like: funcCall(args)
export const FUNCTION_KEYWORDS = /^(function|def|fn|void|static|public|private|native|abstract)/g
// export const OPENING_BRACKET_REGEX = /([([{])$/
export const CLOSING_BRACKET_REGEX = /([)\]}])$/


const INDENTATION_REGEX = /^[\t ]*/

/**
 * 计算代码行的缩进级别
 * @param line - 要分析的代码行
 * @param indentation - 缩进字符（空格或制表符）
 * @returns 缩进级别数值
 */
export function indentation(line: string): number {
    const regex = line.match(INDENTATION_REGEX);
    if (regex) {
        const whitespace = regex[0];
      return [...whitespace].reduce((p) => p + 1, 0);
      }
    return 0;
}

// Languages with more than 100 multiline completions in the last month and CAR > 20%:
// https://sourcegraph.looker.com/explore/sourcegraph/cody?qid=JBItVt6VFMlCtMa9KOBmjh&origin_space=562
const LANGUAGES_WITH_MULTILINE_SUPPORT = [
  'astro',
  'c',
  'cpp',
  'csharp',
  'css',
  'dart',
  'elixir',
  'go',
  'html',
  'java',
  'javascript',
  'javascriptreact',
  'kotlin',
  'php',
  'python',
  'rust',
  'svelte',
  'typescript',
  'typescriptreact',
  'vue',
]
export function shouldTriggerSingleline(context: CompletionContext) {
  const {language, currentLinePrefix, currentLineSuffix, prefix, suffix, suffixLines, prefixLines} = context;

  // 缓存当前行文本，避免重复计算
  const currentLineText = currentLineSuffix.trim().length > 0 
    ? `${currentLinePrefix}${currentLineSuffix}` 
    : currentLinePrefix;

  const isMultilineSupported = LANGUAGES_WITH_MULTILINE_SUPPORT.includes(language);
  
  // 如果语言不支持多行补全,直接返回true
  if (!isMultilineSupported) {
    return true;
  }

  // 检查是否是函数定义
  const isFunctionDefinition = FUNCTION_KEYWORDS.test(currentLinePrefix.trim());
  if (isFunctionDefinition) {
    return false;
  }

  // 检查是否是未完成的表达式
  if (currentLineText.trim().endsWith('=') || 
      currentLineText.includes('if (') ||
      currentLineText.includes('while (')) {
    return false;
  }

  // 检查是否是注释
  if (currentLineText.trim().startsWith('#') || 
      currentLineText.trim().startsWith('//')) {
    return false;
  }

  // 检查是否在块开始处
  const blockStart = endsWithBlockStart(prefix, language);
  if (blockStart) {
    return false;
  }

  // 检查是否在块内的空行
  if (currentLinePrefix.trim() === '' && 
      suffixLines.length >= 1 && 
      suffixLines[0]?.trim() === '') {
    return false;
  }

  // 检查是否是函数/方法调用
  const isMethodOrFunctionInvocation = 
    !isFunctionDefinition && 
    FUNCTION_OR_METHOD_INVOCATION_REGEX.test(currentLineText);
  if (isMethodOrFunctionInvocation) {
    return true;
  }

  // 检查是否在块上下文中
  if (prefix.trim().endsWith('{') && suffix.trim().startsWith('}')) {
    return false;
  }

  // 检查缩进结构
  const prevNonEmptyLine = [...prefixLines].reverse().find(line => !isBlank(line)) || ''; // deep clone to avoid mutating the original array, bugfix
  const nextNonEmptyLine = suffixLines.find(line => !isBlank(line)) || '';

  const isInBlockContext = 
    prevNonEmptyLine && 
    indentation(prevNonEmptyLine) < indentation(currentLinePrefix) &&
    (!nextNonEmptyLine || indentation(prevNonEmptyLine) >= indentation(nextNonEmptyLine));

  if (isInBlockContext) {
    return false;
  }

  // 默认使用单行补全
  return true;
}

export function endsWithBlockStart(text: string, languageId: string): string | null {
  const blockStart = getLanguageConfig(languageId)?.blockStart
  return blockStart && text.trimEnd().endsWith(blockStart) ? blockStart : null
}
export function containsModifier(input: string): boolean {
  const modifiers = [
    "public", "private", "protected", "class", "interface"
  ];

  const words = input.split(/\s+/);

  return words.some(word => modifiers.includes(word));
}
/**
 * 计算代码行的缩进级别
 * @param line - 要分析的代码行
 * @param indentation - 缩进字符（空格或制表符）
 * @returns 缩进级别数值
 */
export function getIndentationLevel(line: string, indentation?: string): number {
  // Early return for empty lines
  if (!line) return 0;

  let count = 0;
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    if (char !== ' ' && char !== '\t') break;
    
    if (indentation === undefined) {
      count++;
    } else if (indentation === '\t') {
      if (char === '\t') count++;
    } else if (indentation.match(/^ *$/)) {
      // For space-based indentation
      if (char === ' ') count++;
    } else {
      throw new Error(`Invalid indentation: ${indentation}`);
    }
  }

  // If using space-based custom indentation, divide by indentation length
  return indentation?.match(/^ *$/) 
    ? count / indentation.length 
    : count;
}

/**
 * 检查给定行是否为代码块的结束行
 * @param lines - 代码行数组
 * @param index - 当前行索引
 * @returns 是否为块结束行
 */
export function isBlockClosingLine(lines: string[], index: number): boolean {
  if (index <= 0 || index > lines.length - 1) {
    return false;
  }
  return getIndentationLevel(lines[index - 1]!) > getIndentationLevel(lines[index]!);
}

// FIXME: This function is not good enough, it can not handle escaped characters.
export function findUnpairedAutoClosingChars(input: string): string {
  const stack: string[] = [];

  for (const char of input) {
    [
      ["(", ")"],
      ["[", "]"],
      ["{", "}"],
    ].forEach((pair) => {
      if (char === pair[1]) {
        if (stack.length > 0 && stack[stack.length - 1] === pair[0]) {
          stack.pop();
        } else {
          stack.push(char);
        }
      }
    });
    if ("([{".includes(char)) {
      stack.push(char);
    }
    ["'", '"', "`"].forEach((quote) => {
      if (char === quote) {
        if (stack.length > 0 && stack.includes(quote)) {
          stack.splice(stack.lastIndexOf(quote), stack.length - stack.lastIndexOf(quote));
        } else {
          stack.push(char);
        }
      }
    });
  }
  return stack.join("");
}

// Using string levenshtein distance is not good, because variable name may create a large distance.
// Such as distance is 9 between `const fooFooFoo = 1;` and `const barBarBar = 1;`, but maybe 1 is more accurate.
// May be better to count distance based on words instead of characters.
import * as levenshtein from "fast-levenshtein";
import { CompletionContext } from "./CompletionContext";
export function calcDistance(a: string, b: string) {
  return levenshtein.get(a, b);
}

// Polyfill for AbortSignal.any(signals) which added in Node.js v20.
export function abortSignalFromAnyOf(signals: (AbortSignal | undefined)[]) {
  const controller = new AbortController();
  for (const signal of signals) {
    if (signal?.aborted) {
      controller.abort(signal.reason);
      return signal;
    }
    signal?.addEventListener("abort", () => controller.abort(signal.reason), {
      signal: controller.signal,
    });
  }
  return controller.signal;
}



export const regOnlyAutoClosingCloseChars = /^([)\]}>"'`]|(\/>))*$/g;

/**
 * 执行 GET 请求，支持超时和重试机制
 * @param url - 请求URL
 * @param timeout - 超时时间（毫秒）
 * @param signal - 中止信号
 * @param retries - 重试次数
 * @returns Promise<Response>
 */
export async function fetchGet(url: string, timeout: number, signal?: AbortSignal, retries = 1): Promise<Response> {
  // 添加重试逻辑
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      signal?.addEventListener('abort', () => controller.abort());
      
      try {
        const response = await fetch(url, {
          method: 'GET',
          signal: controller.signal,
        });
        
        return response;
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error: any) {
      if (attempt === retries || error.name === 'AbortError') {
        throw error;
      }
      // 指数退避重试
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
    }
  }
  throw new Error('Maximum retries exceeded');
}

/**
 * 执行 POST 请求，支持超时和重试机制
 * @param url - 请求URL
 * @param data - 请求数据
 * @param timeoutMillis - 超时时间（毫秒）
 * @param externalSignal - 外部中止信号
 * @param headers - 自定义请求头
 * @param retries - 重试次数
 * @returns Promise<Response>
 */
export async function fetchPost(
  url: string, 
  data: any, 
  timeoutMillis: number, 
  externalSignal?: AbortSignal, 
  headers?: Record<string, string>,
  retries = 1
): Promise<Response> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMillis);
      
      externalSignal?.addEventListener('abort', () => controller.abort());
      
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers // 合并自定义请求头
          },
          body: JSON.stringify(data),
          signal: controller.signal || externalSignal,
        });
        
        return response;
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error: any) {
      if (attempt === retries || error.name === 'AbortError') {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
    }
  }
  throw new Error('Maximum retries exceeded');
}