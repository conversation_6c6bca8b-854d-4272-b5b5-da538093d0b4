const fs = require('fs');
const path = require('path');

// 目录和文件路径
const distDir = path.join(__dirname, 'dist');
const indexJsPath = path.join(distDir, 'index.js');
const indexJsMapPath = path.join(distDir, 'index.js.map');

/**
 * 清理构建目录，仅保留wasm目录,index.js,onnx,onnx-bin
 */
function cleanupBuild() {
  console.log('🚀 开始清理构建输出 (新版)...');

  try {
    // 确保dist目录存在
    if (!fs.existsSync(distDir)) {
      console.error('❌ dist目录不存在，构建可能失败');
      process.exit(1);
    }

    // 确保关键文件由 tsup 生成
    if (!fs.existsSync(indexJsPath)) {
      console.error('❌ dist/index.js 文件不存在，tsup 构建可能未按预期运行');
      process.exit(1);
    }
    if (!fs.existsSync(indexJsMapPath)) {
      console.warn('⚠️  dist/index.js.map 文件不存在，source map 可能缺失');
    }

    // 注意：wasmDir 的原始路径是 out/wasm。如果 tsup 的 sharedAssets copy 插件配置
    // from: "./wasm/*" to: "./wasm" 并且 outDir 是 dist，它会拷贝到 dist/wasm。
    // 需要确认 wasm 的最终位置和拷贝逻辑是否依然正确。
    // 假设 sharedAssets 仍然将 wasm 等拷贝到 dist 目录。
    const wasmDistDir = path.join(distDir, 'wasm');
    if (!fs.existsSync(wasmDistDir)) {
      console.warn('⚠️  dist/wasm 目录不存在');
    }

    // 获取dist目录下的所有文件和目录
    const items = fs.readdirSync(distDir);

    // 保留的文件和目录列表 (现在基于 dist 目录)
    const keepItems = ['index.js', 'index.js.map', 'wasm', 'bin', 'cpl-rf-predictor.onnx', 'util.exe'];

    for (const item of items) {
      const itemPath = path.join(distDir, item);

      if (keepItems.includes(item)) {
        continue;
      }

      // 删除其他所有文件和目录
      if (fs.lstatSync(itemPath).isDirectory()) {
        fs.rmSync(itemPath, { recursive: true, force: true });
        console.log(`🗑️ 已删除目录: ${item}`);
      } else {
        fs.unlinkSync(itemPath);
        console.log(`🗑️ 已删除文件: ${item}`);
      }
    }

    console.log('🎉 清理完成 (新版)!');
  } catch (error) {
    console.error('❌ 发生错误:', error.message);
    process.exit(1);
  }
}

// 执行清理操作
cleanupBuild();
