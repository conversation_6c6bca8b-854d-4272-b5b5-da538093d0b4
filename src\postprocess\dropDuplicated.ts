import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { splitLines, isBlank, calcDistance, joinLines } from "../utils";

/**
 * 创建一个过滤器，移除与上下文重复的补全项
 * 
 * 检查补全内容是否与前缀或后缀内容重复或高度相似，
 * 如果重复则移除该补全项
 * 
 * @returns 过滤重复补全项的过滤器函数
 */
export function dropDuplicated(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    // 快速路径：空输入或极短输入直接返回
    if (!input || input.trim().length <= 1) {
      return input;
    }

    const { suffixLines, prefixLines } = context;
    const inputLines = splitLines(input);

    // 优化：使用单次遍历找到第一个非空行的索引
    let inputIndex = 0, suffixIndex = 0, prefixIndex = prefixLines.length - 1;
    
    while (inputIndex < inputLines.length && isBlank(inputLines[inputIndex]!)) inputIndex++;
    while (suffixIndex < suffixLines.length && isBlank(suffixLines[suffixIndex]!)) suffixIndex++;
    while (prefixIndex >= 0 && isBlank(prefixLines[prefixIndex]!)) prefixIndex--;

    // 计算需要比较的行数
    const lineCountSuffix = Math.min(3, inputLines.length - inputIndex, suffixLines.length - suffixIndex);
    const lineCountPrefix = Math.min(3, inputLines.length - inputIndex, prefixIndex + 1);

    // 快速路径：如果没有可比较的行，直接返回
    if (lineCountSuffix < 1 && lineCountPrefix < 1) {
      return input;
    }

    // 检查前缀重复
    if (lineCountPrefix >= 1) {
      const inputToCompare = inputLines
        .slice(inputIndex, inputIndex + lineCountPrefix)
        .join("")
        .trim();
      const prefixToCompare = prefixLines
        .slice(prefixIndex + 1 - lineCountPrefix, prefixIndex + 1)
        .join("")
        .trim();

      // 优化：使用更合理的阈值计算
      const thresholdPrefix = Math.max(
        inputToCompare.length <= 3 ? 2 : 1,
        Math.floor(0.05 * Math.min(inputToCompare.length, prefixToCompare.length))
      );

      if (calcDistance(inputToCompare, prefixToCompare) <= thresholdPrefix) {
        return null;
      }
    }

    // 检查后缀重复
    if (lineCountSuffix >= 1) {
      const inputToCompare = inputLines
        .slice(inputIndex, inputIndex + lineCountSuffix)
        .join("")
        .trim();
      const suffixToCompare = suffixLines
        .slice(suffixIndex, suffixIndex + lineCountSuffix)
        .join("")
        .trim();

      // 优化：统一阈值计算逻辑
      const thresholdSuffix = Math.max(1, 
        Math.floor(0.05 * Math.min(inputToCompare.length, suffixToCompare.length))
      );

      if (calcDistance(inputToCompare, suffixToCompare) <= thresholdSuffix) {
        return null;
      }
    }

    // 优化：处理空输入情况
    if (inputLines.length === 0) {
      return null;
    }

    // 优化：使用更简洁的最后一行处理逻辑
    let lastNonEmptyLine = inputLines[inputLines.length - 1]?.trim();
    if (!lastNonEmptyLine && inputLines.length > 1) {
      lastNonEmptyLine = inputLines[inputLines.length - 2]?.trim();
    }

    // 处理后缀行重复
    const suffixFirstLines = suffixLines.slice(0, Math.min(inputLines.length, suffixLines.length));
    if (suffixFirstLines[0]?.trim() === "") {
      suffixFirstLines.shift(); // 使用 shift 替代 slice
    }

    // 优化：简化重复检测逻辑
    if (lastNonEmptyLine && lastNonEmptyLine.length > 1 && suffixFirstLines.some(line => 
      line.trim().startsWith(lastNonEmptyLine || '')
    )) {
      while (lastNonEmptyLine && lastNonEmptyLine.length > 1 && 
             suffixFirstLines.some(line => line.trim().startsWith(lastNonEmptyLine || ''))) {
        inputLines.pop();
        lastNonEmptyLine = inputLines[inputLines.length - 1]?.trim();
      }
      return inputLines.length > 0 ? joinLines(inputLines) : null;
    }

    return input;
  };
}
