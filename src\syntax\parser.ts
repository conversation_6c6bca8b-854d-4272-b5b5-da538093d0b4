import path from "path";
import TreeSitterParser from "web-tree-sitter";
import { isTest } from "../env";

/**
 * 语言配置映射表
 * 将VSCode语言标识符映射到Tree-sitter语言配置
 * @see https://code.visualstudio.com/docs/languages/identifiers
 */
export const languagesConfigs: Record<string, string> = {
  javascript: "tsx",
  typescript: "tsx",
  javascriptreact: "tsx",
  typescriptreact: "tsx",
  python: "python",
  java: "java",
  go: "go",
  rust: "rust",
  ruby: "ruby",
};

/** Tree-sitter初始化标记 */
let treeSitterInitialized = false;

/**
 * 创建Tree-sitter解析器实例
 * @param languageConfig - 语言配置名称
 * @returns Tree-sitter解析器实例
 */
async function createParser(languageConfig: string): Promise<TreeSitterParser> {
  // 首次使用时初始化Tree-sitter
  if (!treeSitterInitialized) {
    try {
      await TreeSitterParser.init({
        // 根据环境确定WASM文件位置
        locateFile(scriptName: string, scriptDirectory: string) {
          const paths = isTest ? [scriptDirectory, scriptName] : [scriptDirectory, "wasm", scriptName];
          return path.join(...paths);
        },
      });
    }
    catch (error) {
      console.log("error is" + error);
    }

    treeSitterInitialized = true;
  }

  // 创建新的解析器实例
  const parser = new TreeSitterParser();
  
  // 确定语言WASM文件路径
  const langWasmPaths = isTest
    ? [process.cwd(), "wasm", `tree-sitter-${languageConfig}.wasm`]
    : [__dirname, "wasm", `tree-sitter-${languageConfig}.wasm`];

  // 加载语言配置并设置
  parser.setLanguage(await TreeSitterParser.Language.load(path.join(...langWasmPaths)));
  return parser;
}

/** 解析器缓存映射 */
const parsers = new Map<string, TreeSitterParser>();

/**
 * 获取指定语言的Tree-sitter解析器
 * 如果缓存中不存在则创建新实例
 * @param languageConfig - 语言配置名称
 * @returns Tree-sitter解析器实例
 */
export async function getParser(languageConfig: string): Promise<TreeSitterParser> {
  // 尝试从缓存获取解析器
  let parser = parsers.get(languageConfig);
  if (!parser) {
    // 缓存未命中时创建新实例
    parser = await createParser(languageConfig);
    // 存入缓存
    parsers.set(languageConfig, parser);
  }

  return parser;
}
