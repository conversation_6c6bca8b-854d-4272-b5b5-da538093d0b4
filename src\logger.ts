import path from "path";
import * as winston from "winston";

// 先确保环境变量模块已加载
import "./env";

/**
 * 获取用户主目录路径
 * Windows: %USERPROFILE%
 * Unix: $HOME
 */
const homePath: string | undefined =
  process.env["HOME"] || process.env["USERPROFILE"];

// 从全局配置获取必要参数
const pluginType = global.passingParameters.pluginType;
const pluginVersion = global.passingParameters.pluginVersion;
const clientType = global.passingParameters.clientType;
const clientVersion = global.passingParameters.clientVersion;
const serverType = global.passingParameters.serverType;
const env = global.passingParameters.env;

// 构建日志标识符和日志目录路径
const logIdentifier = `${pluginType}-${pluginVersion}-${clientType}-${clientVersion}`;
const logDirname =
  serverType === "codefree"
    ? path.join(homePath ?? "C:", ".codefree", "cpl-agent-logs", logIdentifier)
    : path.join(homePath ?? "C:", ".secidea", "cpl-agent-logs", logIdentifier);
const logLevel = env === "prod" ? "info" : "debug";
// const logLevel = "debug";// 本地调试用

/**
 * 创建新的日志记录器实例
 * 配置了文件和控制台两个传输目标
 */
export const logger = winston.createLogger({
  /** 默认日志级别 */
  level: logLevel,
  /** 日志格式配置 */
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  /** 日志传输配置 */
  transports: [
    // 文件传输配置
    new winston.transports.File({
      /** 日志文件名 */
      filename: "cpl-agent.log",
      /** 日志文件目录 */
      dirname: logDirname,
      /** 单个日志文件最大大小(约50MB) */
      maxsize: 51200000,
      /** 最大保留文件数 */
      maxFiles: 10,
    }),
    // 控制台传输配置
    new winston.transports.Console({
      /** 使用简单格式输出到控制台 */
      format: winston.format.simple(),
    }),
  ],
});

// 记录环境变量信息
logger.info("Logger initialized with environment variables", {
  pluginType,
  pluginVersion,
  homePath,
  logDirname,
  env,
});
