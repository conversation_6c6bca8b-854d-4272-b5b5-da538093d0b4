import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { isBlank, splitLines } from "../utils";

/**
 * 检测代码的缩进风格
 * 分析代码行以确定使用的缩进类型（制表符、2空格或4空格）
 * 
 * @param lines - 要分析的代码行数组
 * @returns 检测到的缩进字符串，如果无法检测则返回null
 */
function detectIndentation(lines: string[]): string | null {
  const matches = {
    "\t": 0,  // 制表符计数
    "  ": 0,  // 2空格计数
    "    ": 0, // 4空格计数
  };
  
  // 遍历每一行检测缩进类型
  for (const line of lines) {
    if (line.match(/^\t/)) {
      matches["\t"]++;
    } else {
      const spaces = line.match(/^ */)?.[0].length ?? 0;
      if (spaces > 0) {
        if (spaces % 4 === 0) {
          matches["    "]++;
        }
        if (spaces % 2 === 0) {
          matches["  "]++;
        }
      }
    }
  }

  // 按优先级返回检测结果
  if (matches["\t"] > 0) {
    return "\t";
  }
  if (matches["  "] > matches["    "]) {
    return "  ";
  }
  if (matches["    "] > 0) {
    return "    ";
  }
  return null;
}

/**
 * 获取代码行的缩进级别
 * 
 * @param line - 要分析的代码行
 * @param indentation - 缩进字符串（制表符或空格）
 * @returns 缩进级别（0表示无缩进）
 */
function getIndentLevel(line: string, indentation: string): number {
  if (indentation === "\t") {
    return line.match(/^\t*/g)?.[0].length ?? 0;
  } else {
    const spaces = line.match(/^ */)?.[0].length ?? 0;
    return spaces / indentation.length;
  }
}

/**
 * 创建一个格式化缩进的过滤器
 * 根据上下文和检测到的缩进风格，统一调整代码的缩进
 * 
 * @returns 格式化缩进的过滤器函数
 */
export function formatIndentation(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const { prefixLines, suffixLines, currentLinePrefix, indentation } = context;
    const inputLines = splitLines(input);

    // 如果没有指定缩进，保持原样
    if (!indentation) {
      return input;
    }

    // 如果上下文中已有缩进，假设服务器输出已学习到正确的缩进
    const prefixLinesForDetection = isBlank(currentLinePrefix)
      ? prefixLines.slice(0, prefixLines.length - 1)
      : prefixLines;
    if (prefixLines.length > 1 && detectIndentation(prefixLinesForDetection) !== null) {
      return input;
    }
    const suffixLinesForDetection = suffixLines.slice(1);
    if (suffixLines.length > 1 && detectIndentation(suffixLinesForDetection) !== null) {
      return input;
    }

    // 检查输入是否已经使用了正确的缩进
    const inputLinesForDetection = inputLines.map((line, index) => {
      return index === 0 ? currentLinePrefix + line : line;
    });
    const inputIndentation = detectIndentation(inputLinesForDetection);
    if (inputIndentation === null || inputIndentation === indentation) {
      return input;
    }

    // 处理每一行的缩进
    const formatted = inputLinesForDetection.map((line, index) => {
      // 获取当前行的缩进级别
      const level = getIndentLevel(line, inputIndentation);
      
      // 顶层代码不调整缩进
      if (level === 0) {
        return inputLines[index];
      }

      // 提取行内容（不含缩进）
      const rest = line.slice(inputIndentation.length * level);

      // 处理第一行
      if (index === 0) {
        if (!isBlank(currentLinePrefix)) {
          return inputLines[0];
        } else {
          return indentation.repeat(level).slice(currentLinePrefix.length) + rest;
        }
      } else {
        // 处理其他行
        return indentation.repeat(level) + rest;
      }
    });

    // 重新组合所有行
    return formatted.join("");
  };
}
