import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { splitLines } from "../utils";

/**
 * 创建一个处理单行模式下多行内容的过滤器
 * 在单行补全模式下，如果输入包含多行，尝试保留第一行有效内容
 * 
 * @returns 处理多行内容的过滤器函数
 */
export function trimMultiLineInSingleLineMode(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const inputLines = splitLines(input);
    
    // 仅在单行模式且输入包含多行时处理
    if (context.mode === "fill-in-line" && inputLines.length > 1) {
      const suffix = context.currentLineSuffix.trimEnd();
      const inputLine = inputLines[0]!.trimEnd();
      
      // 如果第一行以后缀结尾，移除后缀
      if (inputLine.endsWith(suffix)) {
        const trimmedInputLine = inputLine.slice(0, -suffix.length);
        if (trimmedInputLine.length > 0) {
          // logger.debug({ inputLines, trimmedInputLine }, "Trim content with multiple lines");
          return trimmedInputLine;
        }
      }
      // logger.debug({ inputLines }, "Drop content with multiple lines");
      return null;
    }
    return input;
  };
}
