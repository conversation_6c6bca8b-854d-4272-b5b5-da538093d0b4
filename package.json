{"name": "oscap-agent", "version": "0.1.0", "description": "Generic client agent for Tabby AI coding assistant IDE extensions.", "repository": "https://github.com/TabbyML/tabby", "main": "./dist/index.js", "browser": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"tabby-agent": "./dist/index.js"}, "scripts": {"dev": "tsup --watch --no-minify --no-treeshake", "build": "tsc --noEmit && tsup && node cleanup-build.js", "test": "mocha", "test:watch": "env TEST_LOG_DEBUG=1 mocha --watch", "lint": "eslint --fix --ext .ts ./src && prettier --write .", "lint:check": "eslint --ext .ts ./src && prettier --check ."}, "overrides": {"minimatch": "5.1.2"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/dedent": "^0.7.2", "@types/deep-equal": "^1.0.4", "@types/fast-levenshtein": "^0.0.4", "@types/fs-extra": "^11.0.1", "@types/glob": "^7.2.0", "@types/mocha": "^10.0.1", "@types/node": "18.x", "@types/object-hash": "^3.0.0", "@types/sinon": "^17.0.4", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "chai": "^4.3.7", "dedent": "^0.7.0", "esbuild-plugin-copy": "^2.1.1", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "glob": "^7.2.0", "mocha": "^10.2.0", "prettier": "^3.0.0", "sinon": "^20.0.0", "ts-node": "^10.9.1", "tsup": "^7.1.0", "typescript": "^5.3.2"}, "dependencies": {"chokidar": "^3.5.3", "deep-equal": "^2.2.1", "deepmerge-ts": "^5.1.0", "dot-prop": "^8.0.2", "fast-levenshtein": "^3.0.0", "fs-extra": "^11.1.1", "jwt-decode": "^3.1.2", "lru-cache": "^9.1.1", "object-hash": "^3.0.0", "onnxruntime-node": "^1.16.3", "openapi-fetch": "^0.7.6", "portfinder": "^1.0.37", "stats-logscale": "^1.0.7", "toml": "^3.0.0", "uuid": "^9.0.0", "web-tree-sitter": "^0.20.8", "winston": "^3.11.0"}}