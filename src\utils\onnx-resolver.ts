/**
 * ONNX运行时绑定文件解析器
 * 在不同的构建环境中提供onnxruntime_binding.node文件的正确路径
 */

import * as path from 'path';
import * as fs from 'fs';

/**
 * 获取ONNX运行时绑定文件的路径
 * 首先尝试在dist/bin目录下查找，然后回退到node_modules
 */
export function getOnnxBindingPath(): string {
  // 获取当前平台和架构信息
  const platform = process.platform;
  const arch = process.arch;

  // 可能的路径顺序（优先级从高到低）
  const possiblePaths = [
    path.join(__dirname, 'bin', 'napi-v3', platform, arch, 'onnxruntime_binding.node'),
    path.join(__dirname, '..', 'bin', 'napi-v3', platform, arch, 'onnxruntime_binding.node'),
    path.join(__dirname, '..', '..', 'bin', 'napi-v3', platform, arch, 'onnxruntime_binding.node'),
    path.join(__dirname, '..', 'dist', 'bin', 'napi-v3', platform, arch, 'onnxruntime_binding.node'),
    path.join(__dirname, '..', '..', 'node_modules', 'onnxruntime-node', 'bin', 'napi-v3', platform, arch, 'onnxruntime_binding.node')
  ];
  // 打印所有检查的路径
  // possiblePaths.forEach(path => console.log(`Checking path: ${path}, exists: ${fs.existsSync(path)}`));
  
  // 检查每个路径，返回第一个存在的文件路径
  for (const bindingPath of possiblePaths) {
    if (fs.existsSync(bindingPath)) {
      return bindingPath;
    }
  }

  // 如果所有路径都不存在，返回默认路径（将触发正常的模块解析错误）
  return `../bin/napi-v3/${platform}/${arch}/onnxruntime_binding.node`;
}

/**
 * 修改Node.js的模块解析以支持自定义的ONNX绑定加载
 * 在应用启动时调用此函数以确保ONNX绑定能被正确加载
 */
export function setupOnnxBindingResolver(): void {
  try {
    // 加载绑定文件，验证其存在性
    const bindingPath = getOnnxBindingPath();
    if (fs.existsSync(bindingPath)) {
      console.log(`ONNX绑定文件已找到: ${bindingPath}`);
      require(bindingPath);
    } else {
      console.warn(`警告: 找不到ONNX绑定文件: ${bindingPath}`);
    }
  } catch (error) {
    console.error('设置ONNX绑定解析器时出错:', error);
  }
} 