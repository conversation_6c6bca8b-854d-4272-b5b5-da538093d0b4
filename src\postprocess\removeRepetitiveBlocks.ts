import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { isBlank, calcDistance } from "../utils";

/**
 * 获取代码块分隔正则表达式
 * 目前使用空行作为通用分隔符
 * 
 * @param _ - 语言标识符（暂未使用）
 * @returns 代码块分隔的正则表达式
 */
function blockSplitter(_: string) {
  // 暂未实现针对各语言的特定分隔规则
  // 使用空行匹配作为通用方案
  return /\n(\s*)\n/g;
}

/**
 * 创建一个移除重复代码块的过滤器
 * 检测并移除相似度高的连续代码块
 * 
 * @returns 移除重复代码块的过滤器函数
 */
export function removeRepetitiveBlocks(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const inputBlocks = input.split(blockSplitter(context.language));
    let repetitionCount = 0;
    const repetitionThreshold = 2;
    
    // 跳过最后一个块，因为可能未完成
    let index = inputBlocks.length - 2;
    while (index >= 1) {
      if (isBlank(inputBlocks[index]!)) {
        index--;
        continue;
      }
      
      // 查找前一个非空块
      let prev = index - 1;
      while (prev >= 0 && isBlank(inputBlocks[prev]!)) {
        prev--;
      }
      if (prev < 0) break;
      
      // 计算当前块与前一个块的相似度
      const currentBlock = inputBlocks[index]!.trim();
      const previousBlock = inputBlocks[prev]!.trim();
      const threshold = Math.max(0.1 * currentBlock.length, 0.1 * previousBlock.length);
      const distance = calcDistance(currentBlock, previousBlock);
      
      // 如果相似度高于阈值，增加重复计数
      if (distance <= threshold) {
        repetitionCount++;
        index--;
      } else {
        break;
      }
    }
    
    // 如果重复次数超过阈值，移除重复块
    if (repetitionCount >= repetitionThreshold) {
      return inputBlocks
        .slice(0, index + 1)
        .join("")
        .trimEnd();
    }
    return input;
  };
}
