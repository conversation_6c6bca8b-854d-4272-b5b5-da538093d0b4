/**
 * Agent完整配置类型定义
 */
export type AgentConfig = {
  server: {
    /** 补全模式: "精准优先" */
    cplMode: string;
    /** 请求超时时间(毫秒) */
    requestTimeout: number;
  };
  completion: {
    prompt: {
      /** 是否移除自动闭合字符 */
      experimentalStripAutoClosingCharacters: boolean;
      /** 前缀最大行数 */
      maxPrefixLines: number;
      /** 后缀最大行数 */
      maxSuffixLines: number;
      clipboard: {
        /** 剪贴板内容最小字符数 */
        minChars: number;
        /** 剪贴板内容最大字符数 */
        maxChars: number;
      };
    };
    /** 补全超时时间(毫秒) */
    timeout: number;
  };
  postprocess: {
    limitScope: {
      /** 是否优先使用语法解析器而不是缩进 */
      experimentalSyntax: boolean;
      indentation: {
        /**
         * 当补全继续当前行时的作用域限制:
         * false(默认): 行作用域，使用下一个缩进级别作为限制
         * true: 块作用域，使用当前缩进级别作为限制
         */
        experimentalKeepBlockScopeWhenCompletingLine: boolean;
      };
    };
    calculateReplaceRange: {
      /** 是否优先使用语法解析器而不是括号栈 */
      experimentalSyntax: boolean;
    };
  };
};

/**
 * 递归Partial类型
 * 使配置对象的所有属性(包括嵌套属性)都变为可选
 */
type RecursivePartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[]
  ? RecursivePartial<U>[]
  : T[P] extends object | undefined
  ? RecursivePartial<T[P]>
  : T[P];
};

/**
 * 部分Agent配置类型
 * 所有配置项都是可选的
 */
export type PartialAgentConfig = RecursivePartial<AgentConfig>;

/**
 * 默认Agent配置
 */
export const defaultAgentConfig: AgentConfig = {
  server: {
    cplMode: "精准优先",  // 默认补全模式
    requestTimeout: 30000,  // 默认30秒超时
  },
  completion: {
    prompt: {
      experimentalStripAutoClosingCharacters: false,
      maxPrefixLines: 150,  // 上下文最大行数
      maxSuffixLines: 150,
      clipboard: {
        minChars: 3,    // 最小字符数限制
        maxChars: 2000, // 最大字符数限制
      },
    },
    timeout: 5000,      // 补全请求5秒超时
  },
  postprocess: {
    limitScope: {
      experimentalSyntax: true,  // 默认启用语法解析
      indentation: {
        experimentalKeepBlockScopeWhenCompletingLine: false,
      },
    },
    calculateReplaceRange: {
      experimentalSyntax: false,  // 默认不启用语法解析计算替换范围
    },
  },
};
