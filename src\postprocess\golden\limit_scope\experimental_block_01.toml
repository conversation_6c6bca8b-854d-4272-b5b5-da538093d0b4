description = 'Limit scope experimental: limit to block when completing a line'

[config.limitScope.indentation]
experimentalKeepBlockScopeWhenCompletingLine = true

[context]
filepath = 'foo.ts'
language = 'typescript'
# indentation = '  ' # not specified
text = '''
export class Foo {
  private _foo: number;
  
  constructor() {
    this._foo = 1;
  }
  
  update(value): Foo {
    this._foo =├ value;
    return this;┤
  }
}
'''

[expected]
unchanged = true
