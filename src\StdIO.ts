import readline from "readline";
import { Agent, AgentEvent, agentEventNames, AgentFunction } from "./Agent";
import { logger } from "./logger";
// import { isCanceledError } from "./utils";

/**
 * Agent函数请求类型
 * @template T - Agent函数名称
 */
type AgentFunctionRequest<T extends keyof AgentFunction> = [
  /** 请求ID */
  id: number,
  /** 请求数据 */
  data: {
    /** 函数名 */
    func: T;
    /** 函数参数 */
    args: Parameters<AgentFunction[T]>;
  },
];

/**
 * 取消请求类型
 */
type CancellationRequest = [
  /** 请求ID */
  id: number,
  /** 请求数据 */
  data: {
    /** 函数名固定为cancelRequest */
    func: "cancelRequest";
    /** 要取消的请求ID */
    args: [id: number];
  },
];

/** 标准输入输出请求类型 */
type StdIORequest = AgentFunctionRequest<keyof AgentFunction> | CancellationRequest;

/**
 * Agent函数响应类型
 * @template T - Agent函数名称
 */
type AgentFunctionResponse<T extends keyof AgentFunction> = [
  /** 对应请求ID */
  id: number,
  /** 响应数据或null */
  data: ReturnType<AgentFunction[T]> | null,
];

/**
 * Agent事件通知类型
 */
type AgentEventNotification = [
  /** 固定为0 */
  id: 0,
  /** 事件数据 */
  data: AgentEvent,
];

/**
 * 取消请求响应类型
 */
type CancellationResponse = [
  /** 对应请求ID */
  id: number,
  /** 取消结果或null */
  data: boolean | null,
];

/** 标准输入输出响应类型 */
type StdIOResponse = AgentFunctionResponse<keyof AgentFunction> | AgentEventNotification | CancellationResponse;

/**
 * 标准输入输出处理类
 * 处理与Agent的JSON格式通信
 * 每个请求和响应都应该是单行JSON字符串，以换行符结尾
 */
export class StdIO {
  /** Node进程对象 */
  private readonly process: NodeJS.Process = process;
  /** 标准输入流 */
  private readonly inStream: NodeJS.ReadStream = process.stdin;
  /** 标准输出流 */
  private readonly outStream: NodeJS.WriteStream = process.stdout;
  // private readonly logger = rootLogger.child({ component: "StdIO" });

  /** 中止控制器映射 */
  private abortControllers: { [id: string]: AbortController } = {};

  /** Agent实例 */
  private agent?: Agent;

  constructor() {}

  /**
   * 处理输入行
   * @param line - 输入的JSON字符串
   */
  private async handleLine(line: string) {
    let request: StdIORequest;
    try {
      request = JSON.parse(line) as StdIORequest;
    } catch (error) {
      // this.logger.error({ error }, `Failed to parse request: ${line}`);
      return;
    }
    logger.debug("Received request" + JSON.stringify(request));
    const response = await this.handleRequest(request);
    this.sendResponse(response);
    // this.logger.debug({ response }, "Sent response");
  }

  /**
   * 处理请求
   * @param request - 请求对象
   * @returns 响应对象
   */
  private async handleRequest(request: StdIORequest): Promise<StdIOResponse> {
    let requestId: number = 0;
    const response: StdIOResponse = [0, null];
    const abortController = new AbortController();
    try {
      if (!this.agent) {
        throw new Error(`Agent not bound.\n`);
      }
      requestId = request[0];
      response[0] = requestId;

      const funcName = request[1].func;
      if (funcName === "cancelRequest") {
        response[1] = this.cancelRequest(request as CancellationRequest);
      } else {
        const func = this.agent[funcName];
        if (!func) {
          throw new Error(`Unknown function: ${funcName}`);
        }
        const args = request[1].args;
        // If the last argument is an object and has `signal` property, replace it with the abort signal.
        if (args.length > 0 && typeof args[args.length - 1] === "object" && args[args.length - 1]["signal"]) {
          this.abortControllers[requestId] = abortController;
          args[args.length - 1]["signal"] = abortController.signal;
        }
        // @ts-expect-error TS2684: FIXME
        response[1] = await func.apply(this.agent, args);
      }
    } catch (error) {
      console.error("cpl-agent error: " + error);
      // if (isCanceledError(error)) {
      //   // this.logger.debug({ error, request }, `Request canceled`);
      // } else {
      //   // this.logger.error({ error, request }, `Failed to handle request`);
      // }
    } finally {
      if (this.abortControllers[requestId]) {
        delete this.abortControllers[requestId];
      }
    }
    return response;
  }

  /**
   * 取消请求
   * @param request - 取消请求对象
   * @returns 是否成功取消
   */
  private cancelRequest(request: CancellationRequest): boolean {
    const targetId = request[1].args[0];
    const controller = this.abortControllers[targetId];
    if (controller) {
      controller.abort();
      return true;
    }
    return false;
  }

  /**
   * 发送响应
   * @param response - 响应对象
   */
  private sendResponse(response: StdIOResponse): void {
    this.outStream.write(JSON.stringify(response) + "\n");
  }

  /**
   * 绑定Agent实例
   * @param agent - Agent实例
   */
  bind(agent: Agent): void {
    this.agent = agent;
    for (const eventName of agentEventNames) {
      this.agent.on(eventName, (event) => {
        this.sendResponse([0, event]);
      });
    }
  }

  /**
   * 开始监听输入
   * 处理输入行和进程信号
   */
  listen() {
    readline.createInterface({ input: this.inStream }).on("line", (line) => {
      this.handleLine(line);
    });

    ["SIGTERM", "SIGINT"].forEach((sig) => {
      this.process.on(sig, async () => {
        if (this.agent && this.agent.getStatus() !== "finalized") {
          await this.agent.finalize();
        }
        this.process.exit(0);
      });
    });
  }
}
