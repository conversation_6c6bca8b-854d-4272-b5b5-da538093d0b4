# Tabby Agent

A generic client agent for Tabby AI coding assistant IDE extensions.

## Project Overview

Tabby Agent is a TypeScript library that serves as a client-side agent for AI-powered code completion services. It handles communication with the completion server, processes completion requests and responses, and provides a clean API for IDE extensions to integrate with.

## Prerequisites

- Node.js 18.x or later
- npm or yarn package manager

## Project Structure

```
tabby-agent/
├── dist/               # Compiled output
├── src/               # Source code
│   ├── postprocess/   # Completion post-processing modules
│   ├── syntax/        # Syntax parsing utilities
│   └── ...
├── tests/             # Test files
│   ├── bad_cases/     # Test cases for error handling
│   └── golden/        # Golden test cases
└── wasm/              # WebAssembly modules for syntax parsing
```

## Building the Project

1. Install dependencies:
```bash
npm install
```

2. Build the project:
```bash
npm run build
```

This will:
- Run TypeScript type checking
- Build multiple targets using tsup:
  - CommonJS module (dist/index.js)
  - ES Module for browsers (dist/index.mjs)
  - TypeScript declarations (dist/index.d.ts)
  - CLI tool (dist/out/index.js)

For development with watch mode:
```bash
npm run dev
```

## Running Tests

1. Run all tests:
```bash
npm test
```

2. Run tests in watch mode with debug logging:
```bash
npm run test:watch
```

The test suite includes:
- Unit tests for individual components
- Golden tests for comparing outputs against known good results
- Bad case tests for error handling

## Code Quality

To maintain code quality, run the linter:
```bash
npm run lint
```

To only check for issues without fixing:
```bash
npm run lint:check
```

## Features

1. **Code Completion**: Provides intelligent code completion suggestions based on the current context.
2. **Multi-language Support**: Supports multiple programming languages through Tree-sitter integration.
3. **Caching**: Intelligent caching of completion results for improved performance.
4. **Post-processing**: Various filters and transformations to improve completion quality.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and linting to ensure code quality
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the terms of the LICENSE file included in the repository.