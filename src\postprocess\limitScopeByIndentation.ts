import { CompletionContext } from "../CompletionContext";
import { AgentConfig } from "../AgentConfig";
import { PostprocessFilter } from "./base";
import { isBlank, splitLines } from "../utils";

/**
 * 计算代码行的缩进级别
 * 统计行首空白字符的数量
 * 
 * @param line - 要分析的代码行
 * @returns 缩进字符数量
 */
function calcIndentLevel(line: string): number {
  return line.match(/^[ \t]*/)?.[0]?.length ?? 0;
}

/**
 * 检查是否是缩进块的开始
 * 判断当前行与下一行的缩进关系
 * 
 * @param lines - 代码行数组
 * @param index - 当前行索引
 * @returns 如果下一行缩进更深则返回true
 */
function isOpeningIndentBlock(lines: string[], index: number): boolean {
  if (index < 0 || index >= lines.length - 1) {
    return false;
  }
  return calcIndentLevel(lines[index]!) < calcIndentLevel(lines[index + 1]!);
}

/**
 * 处理上下文信息，确定缩进限制和闭合行规则
 * 
 * @param lines - 补全内容的行数组
 * @param context - 补全上下文
 * @param config - 缩进配置
 * @returns 包含缩进限制和闭合行判断函数的对象
 */
function processContext(
  lines: string[],
  context: CompletionContext,
  config: AgentConfig["postprocess"]["limitScope"]["indentation"],
): { indentLevelLimit: number; allowClosingLine: (closingLine: string) => boolean } {
  let allowClosingLine = false;
  const result = { indentLevelLimit: 0, allowClosingLine: (_: string) => allowClosingLine };
  const { prefixLines, suffixLines, currentLinePrefix } = context;
  if (lines.length == 0 || prefixLines.length == 0) {
    return result; // guard for empty input, technically unreachable
  }
  const isCurrentLineInPrefixBlank = isBlank(currentLinePrefix);
  // if current line is blank, use the previous line as reference
  let referenceLineInPrefixIndex = prefixLines.length - 1;
  while (referenceLineInPrefixIndex >= 0 && isBlank(prefixLines[referenceLineInPrefixIndex]!)) {
    referenceLineInPrefixIndex--;
  }
  if (referenceLineInPrefixIndex < 0) {
    return result; // blank prefix, should be unreachable
  }
  const referenceLineInPrefix = prefixLines[referenceLineInPrefixIndex]!;
  const referenceLineInPrefixIndent = calcIndentLevel(referenceLineInPrefix);

  const currentLineInCompletion = lines[0]!;
  const isCurrentLineInCompletionBlank = isBlank(currentLineInCompletion);
  // if current line is blank, use the next line as reference
  let referenceLineInCompletionIndex = 0;
  while (referenceLineInCompletionIndex < lines.length && isBlank(lines[referenceLineInCompletionIndex]!)) {
    referenceLineInCompletionIndex++;
  }
  if (referenceLineInCompletionIndex >= lines.length) {
    return result; // blank completion, should be unreachable
  }
  const referenceLineInCompletion = lines[referenceLineInCompletionIndex]!;
  let referenceLineInCompletionIndent;
  if (isCurrentLineInCompletionBlank) {
    referenceLineInCompletionIndent = calcIndentLevel(referenceLineInCompletion);
  } else {
    referenceLineInCompletionIndent = calcIndentLevel(currentLinePrefix + referenceLineInCompletion);
  }

  if (!isCurrentLineInCompletionBlank && !isCurrentLineInPrefixBlank) {
    // if two reference lines are contacted at current line, it is continuing uncompleted sentence

    if (config.experimentalKeepBlockScopeWhenCompletingLine) {
      result.indentLevelLimit = referenceLineInPrefixIndent;
    } else {
      result.indentLevelLimit = referenceLineInPrefixIndent + 1; // + 1 for comparison, no matter how many spaces indent
    }
    // allow closing line if first line is opening a new indent block
    allowClosingLine = !!lines[1] && calcIndentLevel(lines[1]) > referenceLineInPrefixIndent;
  } else if (referenceLineInCompletionIndent > referenceLineInPrefixIndent) {
    // if reference line in completion has more indent than reference line in prefix, it is opening a new indent block

    result.indentLevelLimit = referenceLineInPrefixIndent + 1;
    allowClosingLine = true;
  } else if (referenceLineInCompletionIndent < referenceLineInPrefixIndent) {
    // if reference line in completion has less indent than reference line in prefix, allow this closing

    result.indentLevelLimit = referenceLineInPrefixIndent;
    allowClosingLine = true;
  } else {
    // otherwise, it is starting a new sentence at same indent level

    result.indentLevelLimit = referenceLineInPrefixIndent;
    allowClosingLine = true;
  }

  // check if suffix context allows closing line
  // skip 0 that is current line in suffix
  let firstNonBlankLineInSuffix = 1;
  while (firstNonBlankLineInSuffix < suffixLines.length && isBlank(suffixLines[firstNonBlankLineInSuffix]!)) {
    firstNonBlankLineInSuffix++;
  }
  if (firstNonBlankLineInSuffix < suffixLines.length) {
    const firstNonBlankLineInSuffixText = suffixLines[firstNonBlankLineInSuffix]!;
    allowClosingLine &&= calcIndentLevel(firstNonBlankLineInSuffixText) < result.indentLevelLimit;
    result.allowClosingLine = (closingLine: string) => {
      const duplicatedClosingLine =
        closingLine.startsWith(firstNonBlankLineInSuffixText) || firstNonBlankLineInSuffixText.startsWith(closingLine);
      return allowClosingLine && !duplicatedClosingLine;
    };
  }
  return result;
}

/**
 * 创建一个基于缩进的范围限制过滤器
 * 通过分析代码缩进结构来限制补全范围
 * 
 * @param config - 缩进相关配置
 * @returns 基于缩进限制范围的过滤器函数
 */
export function limitScopeByIndentation(
  config: AgentConfig["postprocess"]["limitScope"]["indentation"],
): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const inputLines = splitLines(input);
    
    // 行内补全模式特殊处理
    if (context.mode === "fill-in-line") {
      if (inputLines.length > 1) {
        return null;
      }
    }
    
    // 处理上下文获取缩进限制
    const indentContext = processContext(inputLines, context, config);
    
    // 遍历行检查缩进
    let index;
    for (index = 1; index < inputLines.length; index++) {
      const line = inputLines[index]!;
      const prevLine = inputLines[index - 1]!;
      if (isBlank(line)) {
        continue;
      }
      
      const indentLevel = calcIndentLevel(line);
      if (indentLevel < indentContext.indentLevelLimit) {
        // 如果缩进小于限制且不是新块的开始，考虑是否保留闭合行
        if (isOpeningIndentBlock(inputLines, index)) {
          continue;
        }
        if (indentContext.allowClosingLine(line) && (context.language !== "python" || !isBlank(prevLine))) {
          index++;
        }
        break;
      }
    }
    
    // 如果找到范围边界，截取到该位置
    if (index < inputLines.length) {
      return inputLines.slice(0, index).join("").trimEnd();
    }
    return input;
  };
}
