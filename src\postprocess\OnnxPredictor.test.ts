import { expect } from 'chai';
import { describe, it, before } from 'mocha';
import * as path from 'path';
import * as fs from 'fs';
import { onnxPredictor } from './OnnxPredictor';

describe('ONNX Predictor Tests with Hardcoded Data', () => {
  // 确保模型文件存在
  let modelPath = '';
  
  before(function() {
    // 检查可能的模型文件位置
    const possiblePaths = [
      path.join(__dirname, '../src/postprocess/cpl-rf-predictor.onnx'),
      path.join(__dirname, '../dist/postprocess/cpl-rf-predictor.onnx'),
      path.join(__dirname, '../postprocess/cpl-rf-predictor.onnx')
    ];
    
    for (const potentialPath of possiblePaths) {
      if (fs.existsSync(potentialPath)) {
        console.log(`找到 ONNX 模型文件: ${potentialPath}`);
        console.log(`文件大小: ${fs.statSync(potentialPath).size} 字节`);
        modelPath = potentialPath;
        break;
      }
    }
    
    // 断言模型文件必须存在，否则测试将失败
    if (!modelPath) {
      throw new Error('未找到 ONNX 模型文件！测试无法继续。请确保模型文件存在于以下路径之一：\n' + 
                     possiblePaths.join('\n'));
    }
    
    // 尝试读取模型文件的前20个字节进行分析
    const modelBuffer = fs.readFileSync(modelPath);
    console.log(`成功读取 ${modelBuffer.length} 字节`);
    console.log(`前20个字节: ${modelBuffer.slice(0, 20).toString('hex')}`);
    
    // 检测ONNX版本（如果可能）
    if (modelBuffer.length > 2 && modelBuffer[0] === 0x08) {
      console.log(`检测到ONNX IR版本: ${modelBuffer[1]}`);
    }
    
    // 确保模型已初始化
    return onnxPredictor.initialize();
  });
  
  it('使用Rust示例的测试数据运行预测', async function() {
    // 增加超时时间，ONNX推理可能需要一些时间
    this.timeout(5000);
    
    // 使用来自Rust代码的测试token概率
    const testTokenProbs = [
      { text: "def", prob: -0.1 },
      { text: "calculate", prob: -0.2 },
      { text: "_", prob: -0.1 },
      { text: "sum", prob: -0.3 },
      { text: "(", prob: -0.1 },
      { text: "x", prob: -0.2 },
      { text: ")", prob: -0.1 }
    ];
    
    // 创建上下文信息，模仿Rust代码中的AdditionalInfo
    const context = {
      prefix: "class Calculator:\n    ",
      suffix: "\n        return x + y",
      language: "python",
      is_single_line: false,
      snippets: [
        "def add(x, y):",
        "def sum_numbers(x):"
      ]
    };
    
    console.log("\n测试输入tokens:");
    testTokenProbs.forEach(token => {
      console.log(`Token: ${token.text}, Probability: ${token.prob.toFixed(3)}`);
    });
    
    console.log("\n上下文信息:");
    console.log(`Prefix: ${context.prefix}`);
    console.log(`Suffix: ${context.suffix}`);
    console.log(`Is single line: ${context.is_single_line}`);
    console.log(`Snippets: ${JSON.stringify(context.snippets)}`);
    console.log(`Language: ${context.language}`);
    
    // 直接访问extractFeatures方法提取特征
    const features = (onnxPredictor as any).extractFeatures(testTokenProbs, context);
    
    console.log("\n提取的特征形状:", features.length > 0 ? [1, features[0].length] : [0, 0]);
    const featureString = features[0]?.map((f: number) => f.toFixed(3)).join(", ") || "无特征";
    console.log("特征:", featureString);
    
    const start = performance.now();
    
    // 实际调用shouldAcceptCompletion方法
    const result = await onnxPredictor.shouldAcceptCompletion(testTokenProbs, context);
    
    const duration = performance.now() - start;
    
    console.log("\n预测结果:");
    console.log(`Class: ${result.prediction ? 1 : 0}`);
    console.log(`Probabilities: [${result.probabilities.map(p => p.toFixed(6)).join(", ")}]`);
    console.log(`推理时间: ${duration.toFixed(3)} ms`);
    
    // 验证结果格式
    expect(result).to.have.property('prediction');
    expect(result).to.have.property('probabilities');
    expect(result.probabilities).to.be.an('array');
    expect(result.probabilities.length).to.equal(2);
    
    // 比较特征是否与Rust示例的提取特征匹配（允许一定误差）
    // Rust示例提取的特征: [-0.157, -0.300, -0.100, 0.073, -0.200, 7.000, -0.100, -0.100, 0.200, 0.141, -0.133, 3.135, 3.091, 0.512]
    const expectedFeatures: number[] = [-0.157, -0.300, -0.100, 0.073, -0.200, 7.000, -0.100, -0.100, 0.200, 0.141, -0.133, 3.135, 3.091, 0.512];
    
    if (features[0] && features[0].length === expectedFeatures.length) {
      let featuresMatch = true;
      const tolerance = 0.1; // 允许10%的误差
      
      for (let i = 0; i < expectedFeatures.length; i++) {
        if (i < features[0].length) {
          const featureValue = features[0][i];
          const expectedValue = expectedFeatures[i];
          
          if (featureValue !== undefined && expectedValue !== undefined) {
            const diff = Math.abs(featureValue - expectedValue);
            const relativeDiff = expectedValue !== 0 ? diff / Math.abs(expectedValue) : diff;
            
            if (relativeDiff > tolerance) {
              featuresMatch = false;
              console.log(`特征 #${i} 不匹配: 实际=${featureValue.toFixed(3)}, 预期=${expectedValue.toFixed(3)}`);
            }
          }
        }
      }
      
      if (featuresMatch) {
        console.log("\n特征匹配验证: 通过 ✅");
      } else {
        console.log("\n特征匹配验证: 失败 ❌");
      }
    } else {
      console.log(`\n特征维度不匹配: 实际=${features[0]?.length || 0}, 预期=${expectedFeatures.length}`);
    }
    
    // 验证预测结果是否与Rust示例匹配
    // Rust示例预测结果: Class: 1, Probabilities: [0.327893, 0.672107]
    const expectedClass = 1;
    const expectedProbs: number[] = [0.327893, 0.672107];
    
    const actualClass = result.prediction ? 1 : 0;
    const probTolerance = 0.1; // 允许10%的概率误差
    
    if (result.probabilities.length >= 2 && expectedProbs.length >= 2) {
      const prob0 = result.probabilities[0];
      const prob1 = result.probabilities[1];
      const expProb0 = expectedProbs[0];
      const expProb1 = expectedProbs[1];
      
      if (prob0 !== undefined && prob1 !== undefined && expProb0 !== undefined && expProb1 !== undefined) {
        const prob0Match = Math.abs(prob0 - expProb0) / expProb0 <= probTolerance;
        const prob1Match = Math.abs(prob1 - expProb1) / expProb1 <= probTolerance;
        
        if (actualClass === expectedClass && prob0Match && prob1Match) {
          console.log("\n预测结果验证: 通过 ✅");
        } else {
          console.log("\n预测结果验证: 失败 ❌");
          console.log(`类别: 实际=${actualClass}, 预期=${expectedClass}`);
          console.log(`概率[0]: 实际=${prob0.toFixed(6)}, 预期=${expProb0.toFixed(6)}`);
          console.log(`概率[1]: 实际=${prob1.toFixed(6)}, 预期=${expProb1.toFixed(6)}`);
        }
      } else {
        console.log("\n预测结果验证: 失败 ❌ - 概率值未定义");
      }
    } else {
      console.log("\n预测结果验证: 失败 ❌ - 概率数组长度不匹配");
    }
  });
}); 