description = 'Limit scope experimental: syntax rust: bad case 02'

[config.limitScope]
experimentalSyntax = true

[context]
filepath = 'stop.rs'
language = 'rust'
# indentation = '  ' # not specified
text = '''
fn add(x: i32, y: i32) -> i32 {
    println!("x:├ {}", x);
    println!("y: {}", y);
    println!("x + y: {}┤", x);
    return x + y;
}
'''

[expected]
text = '''
fn add(x: i32, y: i32) -> i32 {
    println!("x:├ {}┤", x);
    return x + y;
}
'''
notEqual = true # FIXME: fix bad case
