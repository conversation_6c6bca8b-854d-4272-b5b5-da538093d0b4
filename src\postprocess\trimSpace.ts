import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { isBlank } from "../utils";
// 主要目的是确保生成的代码补全建议与现有代码行对齐和格式化一致。例如，如果现有代码行的前缀以空格结束，那么补全建议不应该在其开始处添加额外的空格。同样地，如果现有代码行的后缀以空格开始，那么补全建议也不应该在其末尾添加额外的空格。这样可以保持代码的一致性和可读性。

/**
 * 创建一个处理空白字符的过滤器
 * 根据上下文调整补全内容的首尾空白字符，
 * 确保生成的代码补全建议与现有代码行对齐和格式化一致
 * 
 * @returns 处理空白字符的过滤器函数
 */
export function trimSpace(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const { currentLinePrefix, currentLineSuffix } = context;
    let trimmedInput = input;
    
    // 如果前缀以空白结尾，移除输入开头的空白
    if (!isBlank(currentLinePrefix) && currentLinePrefix.match(/\s$/)) {
      trimmedInput = trimmedInput.trimStart();
    }

    // 如果后缀为空或以空白开始，移除输入结尾的空白
    if (isBlank(currentLineSuffix) || (!isBlank(currentLineSuffix) && currentLineSuffix.match(/^\s/))) {
      trimmedInput = trimmedInput.trimEnd();
    }
    return trimmedInput;
  };
}
