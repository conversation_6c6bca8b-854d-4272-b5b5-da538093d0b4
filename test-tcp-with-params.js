const net = require('net');
const { v4: uuidv4 } = require('uuid');

class TcpClientWithParams {
  constructor(host = '127.0.0.1', port = 1983) {
    this.host = host;
    this.port = port;
    this.socket = null;
    this.responseHandlers = new Map();
    this._unfinishedLine = undefined;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.socket = new net.Socket();

      this.socket.connect(this.port, this.host, () => {
        console.log(`[Client] Connected to ${this.host}:${this.port}`);
        resolve();
      });

      this.socket.on('data', (data) => {
        this._handleData(data);
      });

      this.socket.on('error', (error) => {
        console.error('[Client] Connection error:', error);
        reject(error);
      });

      this.socket.on('close', () => {
        console.log('[Client] Connection closed');
        this.socket = null;
      });
    });
  }

  _handleData(data) {
    const d = data.toString();
    const lines = d.split(/\r\n/).filter((line) => line.trim() !== '');
    if (lines.length === 0) {
      return;
    }

    if (this._unfinishedLine) {
      lines[0] = this._unfinishedLine + lines[0];
      this._unfinishedLine = undefined;
    }
    if (!d.endsWith('\r\n')) {
      this._unfinishedLine = lines.pop();
    }
    lines.forEach((line) => this._handleLine(line));
  }

  _handleLine(line) {
    try {
      const message = JSON.parse(line);
      console.log('[Client] Received response for:', message.messageType);

      if (message.messageType === 'agentResponse') {
        const handler = this.responseHandlers.get(message.messageId);
        if (handler) {
          handler(message.data);
          this.responseHandlers.delete(message.messageId);
        }
      } else if (message.messageType === 'agentEvent') {
        console.log('[Client] Agent Event:', message.data);
      }
    } catch (error) {
      console.error('[Client] Error parsing message:', error);
    }
  }

  send(messageType, data, messageId) {
    if (!this.socket) {
      throw new Error('Not connected to server');
    }

    messageId = messageId || uuidv4();
    const message = {
      messageType,
      data,
      messageId,
    };

    console.log(`[Client] Sending ${messageType} request...`);
    this.socket.write(JSON.stringify(message) + '\r\n');
    return messageId;
  }

  async sendAgentRequest(func, args) {
    const requestId = Math.floor(Math.random() * 1000000);
    const request = {
      id: requestId,
      data: {
        func,
        args,
      },
    };

    return new Promise((resolve, reject) => {
      const messageId = this.send('agentRequest', request);

      const timeout = setTimeout(() => {
        this.responseHandlers.delete(messageId);
        reject(new Error('Request timeout'));
      }, 30000);

      this.responseHandlers.set(messageId, (response) => {
        clearTimeout(timeout);
        if (response && response.data !== null) {
          resolve(response.data);
        } else {
          reject(new Error('Request failed'));
        }
      });
    });
  }

  async initialize(config) {
    return this.sendAgentRequest('initialize', [{ config }]);
  }

  async getStatus() {
    return this.sendAgentRequest('getStatus', []);
  }

  async getConfig() {
    return this.sendAgentRequest('getConfig', []);
  }

  async provideCompletions(request) {
    return this.sendAgentRequest('provideCompletions', [request, { signal: null }]);
  }

  disconnect() {
    if (this.socket) {
      this.socket.end();
      this.socket = null;
    }
  }
}

async function testTcpWithParams() {
  const client = new TcpClientWithParams();

  try {
    console.log('🔌 Connecting to TCP server on port 1983...');
    await client.connect();

    console.log('\n📊 Getting agent status...');
    const status = await client.getStatus();
    console.log('✅ Status:', status);

    console.log('\n⚙️  Getting agent config...');
    const config = await client.getConfig();
    console.log('✅ Config keys:', Object.keys(config));
    console.log('✅ Server config:', config.server);

    console.log('\n🧪 Testing code completion with codefree parameters...');
    const completionRequest = {
      id: 'test-codefree-completion-' + Date.now(),
      filepath: 'test.js',
      language: 'javascript',
      text: 'function calculateSum(a, b) {\n  return ',
      position: { line: 1, character: 9 },
      manually: true,
      gitUrls: ['https://github.com/example/repo.git']
    };

    console.log('📝 Completion request:', JSON.stringify(completionRequest, null, 2));

    const completion = await client.provideCompletions(completionRequest);
    console.log('\n📋 Completion result:');
    console.log('- ID:', completion.id);
    console.log('- Choices count:', completion.choices?.length || 0);
    if (completion.choices && completion.choices.length > 0) {
      console.log('- First choice text:', JSON.stringify(completion.choices[0].text));
      console.log('- Replace range:', completion.choices[0].replaceRange);
    }

    // 检查是否有token_probs
    if (completion.token_probs) {
      console.log('- Token probabilities available:', completion.token_probs.length, 'tokens');
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    client.disconnect();
  }
}

// 运行测试
testTcpWithParams();
