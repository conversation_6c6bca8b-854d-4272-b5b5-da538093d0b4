import type { BuildOptions } from "esbuild";
import { copy } from "esbuild-plugin-copy";
import { defineConfig } from "tsup";
import { dependencies } from "./package.json";

// 配置共享的资源拷贝插件，包括ONNX模型文件
const sharedAssets = [
  copy({
    assets: [
      {
        from: "./wasm/*",
        to: "./wasm",
      },
      {
        from: "./src/postprocess/cpl-rf-predictor.onnx",
        to: "./cpl-rf-predictor.onnx",
      },
      {
        from: "./node_modules/onnxruntime-node/bin/**/*",
        to: "./bin",
      },
    ],
  }),
];

// 标记无副作用的包裹函数
function markSideEffectsPlugin(value: boolean, packages: string[]) {
  return {
    name: "sideEffects",
    setup: (build: any) => {
      build.onResolve({ filter: /. */ }, async (args: any) => {
        if (args.pluginData || !packages.includes(args.path)) {
          return;
        }
        const { path, ...rest } = args;
        rest.pluginData = true;
        const result = await build.resolve(path, rest);
        result.sideEffects = value;
        return result;
      });
    },
  };
}

function defineEnvs(targetOptions: BuildOptions, envs: { browser: boolean }) {
  targetOptions["define"] = {
    ...targetOptions["define"],
    "process.env.IS_TEST": "false",
    "process.env.IS_BROWSER": Boolean(envs?.browser).toString(),
  };
  return targetOptions;
}

export default async () => [
  // Temporarily commenting out other builds to isolate the "cli" build
  /*
  defineConfig({
    name: "node-cjs",
    entry: { "library": "src/index.ts" },
    platform: "node",
    target: "node18",
    format: ["cjs"],
    sourcemap: "inline",
    esbuildOptions(options) {
      defineEnvs(options, { browser: false });
    },
    esbuildPlugins: sharedAssets,
    clean: false,
  }),
  defineConfig({
    name: "browser-esm",
    entry: ["src/index.ts"],
    platform: "browser",
    format: ["esm"],
    treeshake: "smallest", // To remove unused libraries in browser.
    sourcemap: true,
    esbuildPlugins: [
      ...sharedAssets,
      polyfillNode({
        polyfills: { fs: true },
      }),
      markSideEffectsPlugin(false, ["chokidar", "rotating-file-stream"]),
    ],
    esbuildOptions(options) {
      defineEnvs(options, { browser: true });
    },
    clean: false,
  }),
  defineConfig({
    name: "type-defs",
    entry: ["src/index.ts"],
    dts: {
      only: true,
    },
    clean: false,
  }),
  */
  defineConfig({
    name: "cli",
    entry: { index: "src/cli.ts" },
    outDir: "./dist",
    platform: "node",
    target: "node18",
    noExternal: Object.keys(dependencies),
    treeshake: false,
    minify: false,
    sourcemap: "inline",
    esbuildPlugins: sharedAssets,
    esbuildOptions(options) {
      defineEnvs(options, { browser: false });
    },
    clean: false,
  }),
];
