import { CompletionResponse, CompletionContext } from "../CompletionContext";
// import { rootLogger } from "../logger";

/**
 * 后处理过滤器类型
 * 接收补全项和上下文，返回处理后的字符串或null
 * @param item - 补全文本项
 * @param context - 补全上下文
 * @returns 处理后的文本或null(表示移除该项)
 */
export type PostprocessFilter = (item: string, context: CompletionContext) => string | null | Promise<string | null>;

// export const logger = rootLogger.child({ component: "Postprocess" });

/**
 * 全局数组扩展
 * 添加distinct方法用于去重
 */
declare global {
  interface Array<T> {
    /**
     * 返回去重后的数组
     * @param identity - 可选的标识函数，用于确定元素唯一性
     * @returns 去重后的数组
     */
    distinct(identity?: (x: T) => any): Array<T>;
  }
}

/**
 * 为Array原型添加distinct方法(如果不存在)
 * 使用Map实现高效去重
 */
if (!Array.prototype.distinct) {
  Array.prototype.distinct = function <T>(this: T[], identity?: (x: T) => any): T[] {
    return [...new Map(this.map((item) => [identity?.(item) ?? item, item])).values()];
  };
}

/**
 * 应用过滤器到补全响应
 * 创建一个处理函数，将指定的过滤器应用到补全响应的每个选项
 * 
 * @param filter - 要应用的过滤器函数
 * @param context - 补全上下文
 * @returns 处理补全响应的函数
 */
export function applyFilter(
  filter: PostprocessFilter,
  context: CompletionContext,
): (response: CompletionResponse) => Promise<CompletionResponse> {
  return async (response: CompletionResponse) => {
    // 对每个选项应用过滤器
    response.choices = (
      await Promise.all(
        response.choices.map(async (choice) => {
          // 计算需要保留的前缀长度
          const replaceLength = context.position - choice.replaceRange.start;
          // 只过滤需要替换的部分
          const filtered = await filter(choice.text.slice(replaceLength), context);
          // 组合保留的前缀和过滤后的文本
          choice.text = choice.text.slice(0, replaceLength) + (filtered ?? "");
          return choice;
        }),
      )
    )
      // 移除空文本的选项
      .filter((choice) => !!choice.text)
      // 移除重复的选项
      .distinct((choice) => choice.text);
    return response;
  };
}
