import type { AgentConfig, PartialAgentConfig } from "./AgentConfig";
import type { CompletionRequest, CompletionResponse, updatedFile } from "./CompletionContext";

export type { CompletionRequest, CompletionResponse };

/**
 * 客户端属性配置接口
 * @property {Record<string, any>} user - 用户相关属性
 * @property {Record<string, any>} session - 会话相关属性
 */
export type ClientProperties = Partial<{
  user: Record<string, any>;
  session: Record<string, any>;
}>;

/**
 * Agent初始化选项接口
 * @property {PartialAgentConfig} config - Agent配置
 * @property {ClientProperties} clientProperties - 客户端属性
 * @property {DataStore} dataStore - 数据存储实例
 */
export type AgentInitOptions = Partial<{
  config: PartialAgentConfig;
  clientProperties: ClientProperties;
}>;


/**
 * 中止信号选项接口
 */
export type AbortSignalOption = { signal: AbortSignal };


/**
 * Agent状态枚举
 * @enum {string}
 * - notInitialized: Agent未初始化
 * - ready: Agent已从服务器获得有效响应
 * - disconnected: Agent连接服务器失败
 * - unauthorized: 需要认证但未完成认证流程或认证令牌过期
 * - finalized: Agent已终止
 */
export type AgentStatus = "notInitialized" | "ready" | "disconnected" | "unauthorized" | "finalized";

/**
 * Agent功能接口
 * 定义了Agent的所有核心功能方法
 */
export interface AgentFunction {
  /**
   * 初始化Agent
   * @param options - 初始化选项
   * @returns 初始化是否成功
   */
  initialize(options?: AgentInitOptions): Promise<boolean>;

  /**
   * 终止Agent
   * @returns 终止是否成功
   */
  finalize(): Promise<boolean>;

  /**
   * 更新Agent配置
   * 配置更新优先级: 默认配置 < 用户配置文件 < initialize方法配置 < updateConfig方法配置
   * @param key - 配置键名，支持点号分隔的嵌套路径
   * @param value - 配置值
   * @returns 更新是否成功
   */
  updateConfig(key: string, value: any): Promise<boolean>;

  /**
   * 清除指定的配置项
   * @param key - 要清除的配置键名
   * @returns 清除是否成功
   */
  clearConfig(key: string): Promise<boolean>;

  /**
   * 获取当前配置
   * @returns 当前完整配置
   */
  getConfig(): AgentConfig;

  /**
   * 获取当前Agent状态
   * @returns 当前状态
   */
  getStatus(): AgentStatus;

  /**
   * 获取项目文件更新
   * @returns 是否更新成功
   */
  updateFiles(files: updatedFile[]): Promise<boolean>

  /**
   * 启动rag服务
   * @param projectPath - 项目路径
   * @returns 是否启动成功
   */
  startRagServer(projectPath: String): Promise<boolean>

  /**
   * 提供代码补全
   * @param request - 补全请求
   * @param projectId - 项目ID
   * @param options - 中止信号选项
   * @returns 补全响应
   */
  provideCompletions(request: CompletionRequest, options?: AbortSignalOption): Promise<CompletionResponse>;

}

/**
 * Agent事件类型定义
 */
export type StatusChangedEvent = {
  event: "statusChanged";
  status: AgentStatus;
};

export type ConfigUpdatedEvent = {
  event: "configUpdated";
  config: AgentConfig;
};


export type AgentEvent = StatusChangedEvent | ConfigUpdatedEvent;

/**
 * Agent支持的所有事件名称数组
 */
export const agentEventNames: AgentEvent["event"][] = [
  "statusChanged",
  "configUpdated",
];

/**
 * Agent事件发射器接口
 */
export interface AgentEventEmitter {
  on<T extends AgentEvent>(eventName: T["event"], callback: (event: T) => void): this;
}

/**
 * 完整Agent类型
 * 组合了Agent功能和事件发射器接口
 */
export type Agent = AgentFunction & AgentEventEmitter;
