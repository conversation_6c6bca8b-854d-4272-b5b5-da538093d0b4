import { getParser, languagesConfigs } from "../syntax/parser";
import { CompletionContext, CompletionResponse } from "../CompletionContext";
import { isBlank, splitLines } from "../utils";
// import { logger } from "./base";

/**
 * 支持语法分析的语言列表
 */
export const supportedLanguages = Object.keys(languagesConfigs);

/**
 * 基于语法分析计算替换范围
 * 使用Tree-sitter解析器分析代码，找到语法正确的替换点
 * 
 * @param response - 补全响应
 * @param context - 补全上下文
 * @returns 处理后的补全响应
 */
export async function calculateReplaceRangeBySyntax(
  response: CompletionResponse,
  context: CompletionContext,
): Promise<CompletionResponse> {
  const { position, prefix, suffix, prefixLines, currentLineSuffix, currentLinePrefix, language } = context;
  
  // 快速路径：如果不支持该语言或后缀为空，直接返回
  if (!supportedLanguages.includes(language) || isBlank(currentLineSuffix.trimEnd())) {
    return response;
  }

  // 获取语言配置和解析器
  const languageConfig = languagesConfigs[language]!;
  const parser = await getParser(languageConfig);
  const suffixText = currentLineSuffix.trimEnd();

  // 处理每个补全选项
  for (const choice of response.choices) {
    // 获取实际补全文本(不包括前缀)
    const completionText = choice.text.slice(position - choice.replaceRange.start);
    const completionLines = splitLines(completionText);
    let replaceLength = 0;
    
    // 初始解析整个文本
    let tree = parser.parse(prefix + completionText + suffix);
    // 获取补全文本末尾位置的语法节点
    let node = tree.rootNode.namedDescendantForIndex(prefix.length + completionText.length);

    // 逐字符尝试，直到找到语法正确的位置
    while (node.hasError() && replaceLength < suffixText.length) {
      replaceLength++;
      
      // 计算当前位置信息
      const row = prefixLines.length - 1 + completionLines.length - 1;
      const column = completionLines.length === 1
        ? (completionLines[0]?.length ?? 0) + currentLinePrefix.length
        : completionLines[completionLines.length - 1]?.length ?? 0;

      // 创建编辑操作
      const editPosition = {
        startIndex: prefix.length + completionText.length,
        oldEndIndex: prefix.length + completionText.length + 1,
        newEndIndex: prefix.length + completionText.length,
        startPosition: { row, column },
        oldEndPosition: { row, column: column + 1 },
        newEndPosition: { row, column },
      };
      
      // 应用编辑到语法树
      tree.edit(editPosition);
      
      // 使用更短的后缀重新解析
      tree = parser.parse(prefix + completionText + suffix.slice(replaceLength), tree);
      node = tree.rootNode.namedDescendantForIndex(prefix.length + completionText.length);
    }

    // 如果找到语法正确的节点，更新替换范围
    if (!node.hasError()) {
      choice.replaceRange.end = position + replaceLength;
      // logger.trace({ context, completion: choice.text, range: choice.replaceRange }, "Adjust replace range by syntax");
    }
  }

  return response;
}
