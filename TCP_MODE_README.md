# Tabby Agent TCP 模式使用指南

本文档介绍如何使用 Tabby Agent 的 TCP 通信模式，该模式主要用于调试和开发目的。

## 概述

Tabby Agent 现在支持两种通信模式：
1. **StdIO 模式**（默认）：通过标准输入输出进行通信
2. **TCP 模式**：通过 TCP 连接进行通信，便于调试和断点调试

## 启用 TCP 模式

### 方法 1：命令行参数

```bash
# 使用默认端口 3000
node dist/index.js --tcp

# 指定端口和主机
node dist/index.js --tcp --port 3001 --host 0.0.0.0
```

### 方法 2：环境变量

```bash
# 启用 TCP 模式
export TABBY_AGENT_TCP_MODE=true
export TABBY_AGENT_TCP_PORT=3000
export TABBY_AGENT_TCP_HOST=127.0.0.1
node dist/index.js

# 或者使用开发模式环境变量
export TABBY_DEVELOPMENT=true
node dist/index.js
```

### 方法 3：在代码中直接使用

```typescript
import { TabbyAgent } from "./TabbyAgent";
import { TcpIO } from "./TcpIO";

const agent = new TabbyAgent();
const tcpIO = new TcpIO(3000, "127.0.0.1");
tcpIO.bind(agent);
await tcpIO.listen();
```

## TCP 协议说明

### 消息格式

所有消息都是 JSON 格式，以 `\r\n` 结尾：

```json
{
  "messageType": "agentRequest",
  "data": {
    "id": 123,
    "data": {
      "func": "provideCompletions",
      "args": [...]
    }
  },
  "messageId": "uuid-string"
}
```

### 消息类型

1. **agentRequest**: 客户端向 Agent 发送的请求
2. **agentResponse**: Agent 向客户端发送的响应
3. **agentEvent**: Agent 向客户端发送的事件通知

### 请求示例

#### 初始化 Agent

```json
{
  "messageType": "agentRequest",
  "data": {
    "id": 1,
    "data": {
      "func": "initialize",
      "args": [{"config": {...}}]
    }
  },
  "messageId": "init-request-1"
}
```

#### 代码补全请求

```json
{
  "messageType": "agentRequest",
  "data": {
    "id": 2,
    "data": {
      "func": "provideCompletions",
      "args": [
        {
          "id": "completion-1",
          "filepath": "test.js",
          "language": "javascript",
          "text": "function hello() {\n  console.log('hello');\n  ",
          "position": {"line": 2, "character": 2},
          "manually": true
        },
        {"signal": null}
      ]
    }
  },
  "messageId": "completion-request-1"
}
```

## 使用测试客户端

项目包含一个测试客户端，可以用来验证 TCP 模式：

```bash
# 首先启动 TCP 模式的 Agent
TABBY_AGENT_TCP_MODE=true node dist/index.js

# 在另一个终端运行测试客户端
node -e "require('./dist/src/TcpClient.js').testTcpClient()"
```

## 调试优势

### 1. 断点调试

在 TCP 模式下，你可以：
- 在 IDE 中设置断点
- 逐步调试代码补全逻辑
- 检查变量状态
- 分析性能问题

### 2. 网络工具

可以使用各种网络工具来监控和调试：
- `telnet localhost 3000` - 手动发送消息
- Wireshark - 分析网络流量
- 自定义客户端 - 编写专门的测试工具

### 3. 并发测试

TCP 模式允许多个客户端连接（虽然当前实现是单客户端），便于测试并发场景。

## 开发示例

### 创建自定义客户端

```typescript
import { TcpClient } from "./src/TcpClient";

async function customTest() {
  const client = new TcpClient("127.0.0.1", 3000);
  
  try {
    await client.connect();
    await client.initialize();
    
    const result = await client.provideCompletions({
      id: "test",
      filepath: "example.ts",
      language: "typescript",
      text: "const x = ",
      position: { line: 0, character: 10 },
      manually: true
    });
    
    console.log("Completion:", result);
  } finally {
    client.disconnect();
  }
}
```

### 性能测试

```typescript
async function performanceTest() {
  const client = new TcpClient();
  await client.connect();
  await client.initialize();
  
  const startTime = Date.now();
  const promises = [];
  
  for (let i = 0; i < 10; i++) {
    promises.push(client.provideCompletions({
      id: `perf-test-${i}`,
      filepath: "test.js",
      language: "javascript", 
      text: `function test${i}() {\n  `,
      position: { line: 1, character: 2 },
      manually: true
    }));
  }
  
  await Promise.all(promises);
  const endTime = Date.now();
  
  console.log(`10 completions took ${endTime - startTime}ms`);
  client.disconnect();
}
```

## 注意事项

1. **安全性**: TCP 模式主要用于开发和调试，不建议在生产环境中使用
2. **性能**: TCP 模式可能比 StdIO 模式有额外的网络开销
3. **连接管理**: 当前实现支持单个客户端连接，新连接会替换旧连接
4. **错误处理**: 确保正确处理网络错误和连接断开

## 故障排除

### 连接被拒绝
- 检查端口是否被占用
- 确认防火墙设置
- 验证主机地址是否正确

### 消息格式错误
- 确保 JSON 格式正确
- 检查消息是否以 `\r\n` 结尾
- 验证必需字段是否存在

### 超时问题
- 检查网络连接
- 增加超时时间
- 验证服务器是否正常响应

## 参考

- [Agent.ts](src/Agent.ts) - Agent 接口定义
- [TcpMessenger.ts](src/TcpMessenger.ts) - TCP 消息传递实现
- [TcpIO.ts](src/TcpIO.ts) - TCP 输入输出处理
- [TcpClient.ts](src/TcpClient.ts) - 测试客户端实现
