import { expect } from "chai";
import { documentContext, inline } from "./testUtils";
import { dropDuplicated } from "./dropDuplicated";

describe("postprocess", () => {
  describe("dropDuplicated", () => {
    it("should drop completion duplicated with suffix", () => {
      const context = {
        ...documentContext`
        let sum = (a, b) => {
          ║return a + b;
        };
        `,
        language: "javascript",
      };
      // completion give a `;` at end but context have not
      const completion = inline`
          ├return a + b;┤
      `;
      expect(dropDuplicated()(completion, context)).to.be.null;
    });

    it("should drop completion similar to suffix", () => {
      const context = {
        ...documentContext`
        let sum = (a, b) => {
          start = torch.cuda.Event(enable_timing=enable_timing)
          end = torch.cuda.Event(e║
          if enable_timing:
              if tensor_parallel > 1:
                  torch.distributed.barrier()
              start.record()
          scores, sequences = [], [input_ids]
        };
        `,
        language: "python",
      };
      // the difference is a `\n`
      const completion = inline`
          ├nable_timing=enable_timing)
            if enable_timing:
              if tensor_parallel > 1:
                torch.distributed.barrier()
              start.record()┤
      `;
      expect(dropDuplicated()(completion, context)).to.eq("nable_timing=enable_timing)\n");
    });

    it("should drop last input line if it's a substring starting from the beginning of the suffix first line", () => {
      const context = {
        ...documentContext`
        train_pipeline = [
          dict(type='LoadImageFromFile'),
          dict(type='LoadAnnotations', reduce_zero_label=True),
          dict(type='Resize', img_scale=(2048, 512), ratio_range=(0.5, 2.0)),
          ║
          dict(type='RandomFlip', prob=0.5),
          dict(type='PhotoMetricDistortion'),
        };
        `,
        language: "python",
      };
      // the difference is a `\n`
      const completion = inline`
          ├dict(type='RandomCrop', crop_size=crop_size, cat_max_ratio=0.75),\n  dict(type='RandomRotate', prob=0.5, degree=30),\n dict(type='RandomFlip', prob=0┤
      `;
      const dropResult = dropDuplicated()(completion, context)
      expect(dropResult).to.eq("dict(type='RandomCrop', crop_size=crop_size, cat_max_ratio=0.75),\n  dict(type='RandomRotate', prob=0.5, degree=30),\n");
    });

    it("trim input from end to start by comparing with first 4 lines of suffix", () => {
      const context = {
        ...documentContext`
            for i_block in range(self.num_res_blocks + 1):
                res_block.append(
                    ResnetBlock(
                        in_channels=block_in,
                        out_channels=block_out,
                        temb_channels=self.temb_ch,
                        dropout=dropout,
                    )
                )
                block_in = block_out
            self.res_blocks.append(nn.ModuleList(res_block))
            if i_level║
                self.upsample_blocks.append(Upsample(block_in, True))
                curr_res = curr_res * 2

        # end
        self.norm_out = Normalize(block_in)
        self.conv_out = torch.nn.Conv2d(
            block_in, out_channels, kernel_size=3, stride=1, padding=1
        `,
        language: "python",
      };
      // the difference is a `\n`
      const completion = inline`
          ├ != 0:
                self.upsample_blocks.append(Upsample(block_in, True))┤
      `;
      const dropResult = dropDuplicated()(completion, context)
      expect(dropResult).to.eq(" != 0:\n");
    });
  });
});
