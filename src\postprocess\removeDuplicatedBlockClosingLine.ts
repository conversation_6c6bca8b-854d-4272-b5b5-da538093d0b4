import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import { isBlank, isBlockClosingLine, splitLines } from "../utils";

/**
 * 创建一个移除重复块闭合行的过滤器
 * 检查补全内容的最后一行是否与后缀文本开始处重复
 * 通常在limitScope之后使用
 * 
 * @returns 移除重复块闭合行的过滤器函数
 */
export function removeDuplicatedBlockClosingLine(): PostprocessFilter {
  return (input: string, context: CompletionContext) => {
    const { suffixLines, currentLinePrefix } = context;

    // 快速路径：短输入直接返回
    if (!input || input.length < 2) {
      return input;
    }

    const inputLines = splitLines(input);
    if (inputLines.length < 2) {
      return input;
    }

    // 缓存最后一行及其缩进
    const lastLineIndex = inputLines.length - 1;
    const lastLine = inputLines[lastLineIndex]!;
    const lastLineTrimmed = lastLine.trimEnd();

    // 仅在最后一行是块闭合行时继续处理
    const inputLinesForDetection = inputLines.map((line: string, index: number) => 
      index === 0 ? currentLinePrefix + line : line
    );
    
    if (!isBlockClosingLine(inputLinesForDetection, lastLineIndex)) {
      return input;
    }

    // 查找第一个非空后缀行
    let suffixBeginningIndex = 0;
    while (suffixBeginningIndex < suffixLines.length && isBlank(suffixLines[suffixBeginningIndex]!)) {
      suffixBeginningIndex++;
    }
    if (suffixBeginningIndex >= suffixLines.length) {
      return input;
    }

    // 比较去除空白后的行
    const suffixBeginningLine = suffixLines[suffixBeginningIndex]!.trimEnd();
    
    // 如果存在重叠，移除最后一行
    if (lastLineTrimmed.startsWith(suffixBeginningLine) || 
        suffixBeginningLine.startsWith(lastLineTrimmed)) {
      return inputLines
        .slice(0, lastLineIndex)
        .join("")
        .trimEnd();
    }

    return input;
  };
}
