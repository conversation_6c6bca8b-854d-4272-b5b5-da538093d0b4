description = 'Remove duplication: similar lines: bad case 01'

[config]
# use default config

[context]
filepath = 'fibonacci.test.ts'
language = 'typescript'
# indentation = '  ' # not specified
text = '''
import { expect } from "chai";
import { fibon<PERSON>ci } from "./fibonacci";

describe("tests: fibonacci", () => {
  it("test case 1", () => {
    const input = 0;
    const expected = 1;
    expect(fi<PERSON><PERSON><PERSON>(input)).to.be.eq(expected);
  });

  ├it("test case 2", () => {
    const input = 1;
    const expected = 1;
    expect(fibon<PERSON><PERSON>(input)).to.be.eq(expected);
  });┤
  
  it("test case 3", () => {
    const input = 2;
    const expected = 2;
    expect(fibon<PERSON><PERSON>(input)).to.be.eq(expected);
  });
});

'''

[expected]
unchanged = true
notEqual = true # FIXME: fix bad case
