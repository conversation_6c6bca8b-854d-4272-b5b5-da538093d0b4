import { CompletionContext, CompletionResponse } from "../CompletionContext";
import { isBlank, findUnpairedAutoClosingChars } from "../utils";
// import { logger } from "./base";

/**
 * 基于括号栈计算替换范围
 * 检测补全文本中未配对的自动闭合字符，并调整替换范围
 * 
 * @param response - 补全响应
 * @param context - 补全上下文
 * @returns 处理后的补全响应
 */
export function calculateReplaceRangeByBracketStack(
  response: CompletionResponse,
  context: CompletionContext,
): CompletionResponse {
  const { currentLineSuffix } = context;
  const suffixText = currentLineSuffix.trimEnd();
  if (isBlank(suffixText)) {
    return response;
  }
  for (const choice of response.choices) {
    const completionText = choice.text.slice(context.position - choice.replaceRange.start);
    const unpaired = findUnpairedAutoClosingChars(completionText);
    if (isBlank(unpaired)) {
      continue;
    }
    if (suffixText.startsWith(unpaired)) {
      choice.replaceRange.end = context.position + unpaired.length;
      // logger.trace(
      //   { context, completion: choice.text, range: choice.replaceRange, unpaired },
      //   "Adjust replace range by bracket stack",
      // );
    } else if (unpaired.startsWith(suffixText)) {
      choice.replaceRange.end = context.position + suffixText.length;
      // logger.trace(
      //   { context, completion: choice.text, range: choice.replaceRange, unpaired },
      //   "Adjust replace range by bracket stack",
      // );
    }
  }
  return response;
}
