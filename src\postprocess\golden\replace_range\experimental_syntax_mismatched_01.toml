description = 'Replace range experimental: syntax mismatched: case 01'

[config.limitScope]
experimentalSyntax = true
[config.calculateReplaceRange]
experimentalSyntax = false

[context]
filepath = 'fibonacci.ts'
language = 'typescript'
# indentation = '  ' # not specified
text = '''
def fibon<PERSON>ci(├n):
  if n == 0:
    return 0
  elif n == 1:
    return 1
  else:
    return fibon<PERSON>ci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2)┤)
'''

[expected]
text = '''
def fibonacci(├n):
┤)╣
'''
