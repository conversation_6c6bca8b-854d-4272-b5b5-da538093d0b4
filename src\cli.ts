#!/usr/bin/env node

/**
 * CLI入口文件
 * 创建TabbyAgent实例并绑定到标准输入输出或TCP
 * 支持通过环境变量或命令行参数启用TCP模式
 */

// 首先导入并初始化环境变量配置
import './env';

import { StdIO } from "./StdIO";
import { TabbyAgent } from "./TabbyAgent";
import { TcpIO } from "./TcpIO";

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    tcp: false,
    port: 1001,
    host: "127.0.0.1"
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--tcp':
        options.tcp = true;
        break;
      case '--port':
        if (i + 1 < args.length) {
          const portStr = args[i + 1];
          if (portStr) {
            options.port = parseInt(portStr, 10);
          }
          i++;
        }
        break;
      case '--host':
        if (i + 1 < args.length) {
          const hostStr = args[i + 1];
          if (hostStr) {
            options.host = hostStr;
          }
          i++;
        }
        break;
      case '--help':
      case '-h':
        console.log(`
Usage: tabby-agent [options]

TCP Options:
  --tcp                      Enable TCP mode instead of stdio
  --port <number>            TCP port to listen on (default: 3000)
  --host <string>            TCP host to bind to (default: 127.0.0.1)

Agent Configuration:
  --api-key <string>         API key for authentication
  --invoker-id <string>      Invoker ID for identification
  --plugin-type <string>     Plugin type identifier
  --plugin-version <string>  Plugin version
  --client-type <string>     Client type identifier
  --client-version <string>  Client version
  --server-type <string>     Server type (e.g., codefree)
  --server-base-url <string> Base URL for the server
  --cpl-subservice <string>  Completion subservice identifier

General:
  --help, -h                 Show this help message

Environment Variables:
  TABBY_AGENT_TCP_MODE=true    Enable TCP mode
  TABBY_AGENT_TCP_PORT=<port>  Set TCP port
  TABBY_AGENT_TCP_HOST=<host>  Set TCP host

  Agent configuration can also be set via environment variables:
  apiKey, invokerId, pluginType, pluginVersion, clientType,
  clientVersion, serverType, serverBaseUrl, cplSubservice
        `);
        process.exit(0);
        break;
    }
  }

  return options;
}

/**
 * 检查环境变量配置
 */
function checkEnvironmentConfig() {
  return {
    tcp: process.env["TABBY_AGENT_TCP_MODE"] === "true" || process.env["TABBY_DEVELOPMENT"] === "true",
    port: process.env["TABBY_AGENT_TCP_PORT"] ? parseInt(process.env["TABBY_AGENT_TCP_PORT"], 10) : 3000,
    host: process.env["TABBY_AGENT_TCP_HOST"] || "127.0.0.1"
  };
}

/**
 * 解析传递参数
 * 支持通过命令行参数传递配置参数
 */
function parsePassingParameters(): any {
  const args = process.argv.slice(2);
  const params: any = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--api-key':
        if (i + 1 < args.length) {
          params.apiKey = args[i + 1];
          i++;
        }
        break;
      case '--invoker-id':
        if (i + 1 < args.length) {
          params.invokerId = args[i + 1];
          i++;
        }
        break;
      case '--plugin-type':
        if (i + 1 < args.length) {
          params.pluginType = args[i + 1];
          i++;
        }
        break;
      case '--plugin-version':
        if (i + 1 < args.length) {
          params.pluginVersion = args[i + 1];
          i++;
        }
        break;
      case '--client-type':
        if (i + 1 < args.length) {
          params.clientType = args[i + 1];
          i++;
        }
        break;
      case '--client-version':
        if (i + 1 < args.length) {
          params.clientVersion = args[i + 1];
          i++;
        }
        break;
      case '--server-type':
        if (i + 1 < args.length) {
          params.serverType = args[i + 1];
          i++;
        }
        break;
      case '--server-base-url':
        if (i + 1 < args.length) {
          params.serverBaseUrl = args[i + 1];
          i++;
        }
        break;
      case '--cpl-subservice':
        if (i + 1 < args.length) {
          params.cplSubservice = args[i + 1];
          i++;
        }
        break;
    }
  }

  return params;
}

async function main() {
  // 解析命令行参数
  const cliOptions = parseArgs();

  // 检查环境变量
  const envOptions = checkEnvironmentConfig();

  // 解析传递参数
  const cliPassingParams = parsePassingParameters();

  // 合并传递参数，命令行参数优先级更高
  const mergedPassingParams = {
    ...global.passingParameters,
    ...cliPassingParams
  };

  // 更新全局传递参数
  global.passingParameters = mergedPassingParams;

  // 合并配置，命令行参数优先级更高
  const config = {
    tcp: cliOptions.tcp || envOptions.tcp,
    port: cliOptions.port !== 3000 ? cliOptions.port : envOptions.port,
    host: cliOptions.host !== "127.0.0.1" ? cliOptions.host : envOptions.host
  };

  // 创建Agent实例
  const agent = new TabbyAgent();

  if (config.tcp) {
    // TCP模式
    console.log(`[CLI] Starting in TCP mode on ${config.host}:${config.port}`);
    const tcpIO = new TcpIO(config.port, config.host);
    tcpIO.bind(agent);
    await tcpIO.listen();
  } else {
    // 标准输入输出模式
    console.log("[CLI] Starting in stdio mode");
    const stdio = new StdIO();
    stdio.bind(agent);
    stdio.listen();
  }
}

// 启动应用
main().catch((error) => {
  console.error("[CLI] Failed to start:", error);
  process.exit(1);
});
