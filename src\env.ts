/**
 * 环境变量配置模块
 */

/**
 * 传递参数接口定义
 */
export interface PassingParameters {
  apiKey: string;
  invokerId: string;
  pluginType: string;
  pluginVersion: string;
  clientType: string;
  clientVersion: string;
  serverType: string;
  serverBaseUrl: string;
  cplSubservice: string;
  env: string;
}

/**
 * 从环境变量中读取传递参数
 * @returns 传递参数对象
 */
export function getPassingParametersFromEnv(): PassingParameters {
  return {
    apiKey: process.env['apiKey'] || '',
    invokerId: process.env['invokerId'] || '',
    pluginType: process.env['pluginType'] || '',
    pluginVersion: process.env['pluginVersion'] || '',
    clientType: process.env['clientType'] || '',
    clientVersion: process.env['clientVersion'] || '',
    serverType: process.env['serverType'] || '',
    serverBaseUrl: process.env['serverBaseUrl'] || '',
    cplSubservice: process.env['cplSubservice'] || '',
    env: process.env['env'] || 'prod',
  };
}

// 确保 global 类型定义包含 passingParameters
declare global {
  var passingParameters: PassingParameters;
}

// 立即初始化全局变量 - 只执行一次
if (!global.passingParameters) {
  global.passingParameters = getPassingParametersFromEnv();
}

/**
 * 是否在测试环境中运行
 * @type {boolean}
 */
export const isTest = !!process.env["IS_TEST"];

export const isBrowser = false;
/**
 * 是否启用测试日志调试
 * @type {boolean}
 */
export const testLogDebug = !!process.env["TEST_LOG_DEBUG"];