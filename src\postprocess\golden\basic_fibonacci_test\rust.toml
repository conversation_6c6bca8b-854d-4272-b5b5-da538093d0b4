description = 'Basic rust function fibonacci'

[config]
# use default config

[context]
filepath = 'fibonacci.rs'
language = 'rust'
# indentation = '  ' # not specified
text = '''
fn fibonacci(├n: u32) -> u32 {
    if n == 0 {
        0
    } else if n == 1 {
        1
    } else {
        fibon<PERSON>ci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2)
    }
}┤)
'''

[expected]
text = '''
fn fibonacci(├n: u32) -> u32 {
    if n == 0 {
        0
    } else if n == 1 {
        1
    } else {
        fibonacci(n - 1) + fibon<PERSON><PERSON>(n - 2)
    }
}┤)╣
'''
