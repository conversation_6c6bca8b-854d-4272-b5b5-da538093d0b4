description = 'Limit scope experimental: limit to block when completing a line: case 02'

[config.limitScope.indentation]
experimentalKeepBlockScopeWhenCompletingLine = true

[context]
filepath = 'checks.js'
language = 'javascript'
# indentation = '  ' # not specified
text = '''
function check(obj) {
  // if obj.a is defined, then return t├rue
  if (obj.a) {
    return true;
  }┤
  return false;
}
'''

[expected]
unchanged = true