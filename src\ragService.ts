import * as cp from 'child_process';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import portfinder from "portfinder";
import { logger } from './logger';


export class RagService {

  // 进程变量
  private ragProcess: cp.ChildProcess | null = null;
  private schedulerProcess: cp.ChildProcess | null = null;
  private projectPath: string;
  private projectId: string;
  private port: number;
  private isStarted: boolean = false;

  /**
   * 构造器
   * @param dirPath 目录路径
   */
  constructor(projectPath: string) {
    this.projectPath = projectPath

    // 根据项目目录获取独一无二project id
    this.projectId = this.generateUniqueIdFromPath(projectPath)

    // 获取默认端口号
    this.port = 8081

    // 启动rag服务
    this.startRagService()
  }

  public setProjectPath(projectPath: string) {
    this.projectPath = projectPath
  }

  public setProjectId(projectId: string) {
    this.projectId = projectId
  }

  public isEnable(): boolean {
    return this.isStarted
  }

  public getRagPort(): number {
    return this.port
  }

  public getProjectId(): string {
    return this.projectId
  }

  /**
   * 确保目录存在
   * @param dirPath 目录路径
   */
  private async ensureDirectoryExistence(dirPath: string): Promise<void> {
    try {
      await fs.promises.mkdir(dirPath, { recursive: true });
    } catch (error) {
      logger.error(`创建目录失败: ${dirPath}`, error);
    }
  }

  /**
   * 启动RAG服务
   */
  public async startRagService(): Promise<number> {

    // 关闭当前server
    if (this.isStarted) {
      this.stopRagService()
    }

    //尝试获取有用端口号

    try {
      this.port = await this.findAvailableRandomPort()
    } catch (error) {
      // 无可用端口号，则使用默认端口号
      logger.error('Error finding available port:', error);
    };

    // 设置初始参数
    const projectPath = this.projectPath;
    const projectId = this.projectId;
    const ragPort = this.port;

    logger.info(`[cpl-agent] 启动RAG服务, 项目路径: ${projectPath}, 项目ID: ${projectId}, 端口: ${ragPort}`);

    // 获取操作系统信息
    const currentOs = process.platform.toLowerCase();

    // 确定util可执行文件路径 - 直接使用dist目录中的util.exe
    const utilPath = path.join(__dirname, currentOs === 'win32' ? 'util.exe' : 'util');

    // 设置数据目录
    const dataDir = path.join(__dirname, 'data', projectId);
    await this.ensureDirectoryExistence(dataDir);
    await this.ensureDirectoryExistence(path.join(dataDir, 'dataset'));
    await this.ensureDirectoryExistence(path.join(dataDir, 'index'));
    await this.ensureDirectoryExistence(path.join(dataDir, 'remoteRepos'));

    // 创建或更新配置文件
    const configPath = path.join(dataDir, 'config.toml');
    const configContent = `
[[repositories]]
name = "${projectId}"
git_url = "file://${projectPath.replace(/\\/g, '\\\\')}"
`;

    try {
      if (fs.existsSync(configPath)) {
        const existingConfig = fs.readFileSync(configPath, 'utf8');
        if (!existingConfig.includes(`name = "${projectId}"`)) {
          fs.appendFileSync(configPath, configContent);
        }
      } else {
        fs.writeFileSync(configPath, configContent);
      }
    } catch (error) {
      logger.error('创建配置文件失败:', error);
    }

    // 设置可执行权限(Linux/macOS)
    if (currentOs === 'linux' || currentOs === 'darwin') {
      try {
        cp.spawnSync('chmod', ['+x', utilPath]);
      } catch (error) {
        logger.error('设置可执行权限失败:', error);
      }
    }


    // 启动RAG服务
    const serverArgs = ['serve', '--host', '127.0.0.1', '--port', ragPort.toString(), '--dir', dataDir];
    this.startUtilProcess(utilPath, serverArgs);

    // 启动调度器进程
    try {
      this.schedulerProcess = cp.spawn(utilPath, ['scheduler', '--now', '--dir', dataDir], {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: true
      });

      // 避免进程成为僵尸进程
      if (this.schedulerProcess.pid !== undefined) {
        this.schedulerProcess.unref();
      }

      // 监听schedulerProcess stdout
      if (this.schedulerProcess.stdout) {
        this.schedulerProcess.stdout.on('data', (data) => {
          logger.info(`调度器服务 STDOUT: ${data.toString()}`);
        });
      }

      // 监听schedulerProcess stderr
      if (this.schedulerProcess.stderr) {
        this.schedulerProcess.stderr.on('data', (data) => {
          logger.error(`调度器服务 STDERR: ${data.toString()}`);
        });
      }

      this.schedulerProcess.on('exit', (code, signal) => {
        logger.debug(`调度器进程退出，退出码: ${code}, 信号: ${signal}`);
        this.schedulerProcess = null;
      });

      this.schedulerProcess.on('error', (error) => {
        logger.error('调度器进程启动失败:', error);
        this.schedulerProcess = null;
      });

      this.isStarted = true
    } catch (error) {
      logger.error('启动调度器进程失败:', error);
    }


    return ragPort;
  }

  /**
   * 启动util进程
   * @param executablePath 可执行文件路径
   * @param args 命令行参数
   */
  public startUtilProcess(executablePath: string, args: string[]): void {
    try {
      // 停止现有进程
      this.stopRagService();

      // 启动新进程
      logger.debug(`启动RAG服务: ${executablePath} ${args.join(' ')}`);
      this.ragProcess = cp.spawn(executablePath, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: true
      });

      // 避免进程成为僵尸进程
      if (this.ragProcess.pid !== undefined) {
        this.ragProcess.unref();
      }

      // 监听stdout
      if (this.ragProcess.stdout) {
        this.ragProcess.stdout.on('data', (data) => {
          logger.info(`RAG服务 STDOUT: ${data.toString()}`);
        });
      }

      // 监听stderr
      if (this.ragProcess.stderr) {
        this.ragProcess.stderr.on('data', (data) => {
          logger.error(`RAG服务 STDERR: ${data.toString()}`);
        });
      }

      // 监听退出事件
      this.ragProcess.on('exit', (code, signal) => {
        logger.debug(`RAG进程退出，退出码: ${code}, 信号: ${signal}`);
        this.ragProcess = null;
      });

      this.ragProcess.on('error', (error) => {
        logger.error('RAG进程启动失败:', error);
        this.ragProcess = null;
      });
    } catch (error) {
      logger.error('启动RAG服务失败:', error);
    }
  }

  /**
   * 停止RAG服务
   */
  public stopRagService(): void {
    if (!this.isStarted) {
      return
    }

    // 停止RAG进程
    if (this.ragProcess && this.ragProcess.pid !== undefined) {
      try {
        process.kill(this.ragProcess.pid);
      } catch (e) {
        // 忽略错误
      }
      this.ragProcess = null;
    }

    // 停止调度器进程
    if (this.schedulerProcess && this.schedulerProcess.pid !== undefined) {
      try {
        process.kill(this.schedulerProcess.pid);
      } catch (e) {
        // 忽略错误
      }
      this.schedulerProcess = null;
    }

    this.isStarted = false
  }

  /**
   * 生成基于路径的唯一ID， 用于补全rag
   * @param filePath 文件路径
   * @returns SHA-256哈希值
   */
  // todo:迁移到util
  public generateUniqueIdFromPath(filePath: string): string {
    return crypto.createHash('sha256').update(filePath).digest('hex');
  }


  /**
   * 获取可用端口号
   * @returns 当前可用端口号
   */
  // todo:迁移到util
  public async findAvailableRandomPort() {
    try {
      const port = await portfinder.getPortPromise();
      return port;
    } catch (error) {
      console.error('[secidea] 查找可用端口时出错:', error);
      process.exit(1);
    }
  }
}
