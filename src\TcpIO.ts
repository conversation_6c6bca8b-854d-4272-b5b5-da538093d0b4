import { AgentFunction, AgentE<PERSON>, Agent, agentEventNames } from "./Agent";
import { TcpMessenger, TcpMessage } from "./TcpMessenger";

/**
 * Agent函数请求类型
 * @template T - Agent函数名称
 */
type AgentFunctionRequest<T extends keyof AgentFunction> = {
  /** 请求ID */
  id: number;
  /** 请求数据 */
  data: {
    /** 函数名 */
    func: T;
    /** 函数参数 */
    args: Parameters<AgentFunction[T]>;
  };
};

/**
 * 取消请求类型
 */
type CancellationRequest = {
  /** 请求ID */
  id: number;
  /** 请求数据 */
  data: {
    /** 函数名固定为cancelRequest */
    func: "cancelRequest";
    /** 要取消的请求ID */
    args: [id: number];
  };
};

/** TCP请求类型 */
type TcpRequest = AgentFunctionRequest<keyof AgentFunction> | CancellationRequest;

/**
 * Agent函数响应类型
 * @template T - Agent函数名称
 */
type AgentFunctionResponse<T extends keyof AgentFunction> = {
  /** 对应请求ID */
  id: number;
  /** 响应数据或null */
  data: ReturnType<AgentFunction[T]> | null;
};

/**
 * Agent事件通知类型
 */
type AgentEventNotification = {
  /** 固定为0 */
  id: 0;
  /** 事件数据 */
  data: AgentEvent;
};

/**
 * 取消请求响应类型
 */
type CancellationResponse = {
  /** 对应请求ID */
  id: number;
  /** 取消结果或null */
  data: boolean | null;
};

/** TCP响应类型 */
type TcpResponse = AgentFunctionResponse<keyof AgentFunction> | AgentEventNotification | CancellationResponse;

/**
 * TCP输入输出处理类
 * 处理与Agent的TCP通信
 * 每个请求和响应都是JSON格式，通过TCP连接传输
 */
export class TcpIO {
  /** TCP消息传递器 */
  private tcpMessenger: TcpMessenger;
  /** 中止控制器映射 */
  private abortControllers: { [id: string]: AbortController } = {};
  /** Agent实例 */
  private agent?: Agent;

  constructor(port?: number, host?: string) {
    this.tcpMessenger = new TcpMessenger(port, host);
    this.setupMessageHandlers();
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandlers() {
    // 监听请求消息 - 直接处理TCP消息
    this.tcpMessenger.on("agentRequest", async (message: TcpMessage) => {
      const request = message.data as TcpRequest;
      const response = await this.handleRequest(request);
      // 直接通过TCP发送响应
      this.tcpMessenger.send("agentResponse", response, message.messageId);
    });

    // 错误处理
    this.tcpMessenger.onError((error) => {
      console.error("[TcpIO] TCP error:", error);
    });
  }

  /**
   * 处理请求
   * @param request - 请求对象
   * @returns 响应对象
   */
  private async handleRequest(request: TcpRequest): Promise<TcpResponse> {
    let requestId: number = 0;
    const response: TcpResponse = { id: 0, data: null };
    const abortController = new AbortController();

    try {
      if (!this.agent) {
        throw new Error(`Agent not bound.\n`);
      }

      requestId = request.id;
      response.id = requestId;

      const funcName = request.data.func;
      if (funcName === "cancelRequest") {
        response.data = this.cancelRequest(request as CancellationRequest);
      } else {
        const func = this.agent[funcName];
        if (!func) {
          throw new Error(`Unknown function: ${funcName}`);
        }

        const args = request.data.args;
        // 如果最后一个参数是对象且有signal属性，替换为中止信号
        if (args.length > 0 && typeof args[args.length - 1] === "object" && args[args.length - 1]["signal"]) {
          this.abortControllers[requestId] = abortController;
          args[args.length - 1]["signal"] = abortController.signal;
        }

        // @ts-expect-error TS2684: FIXME
        response.data = await func.apply(this.agent, args);
      }
    } catch (error) {
      console.error("[TcpIO] Error handling request:", error);
      response.data = null;
    } finally {
      if (this.abortControllers[requestId]) {
        delete this.abortControllers[requestId];
      }
    }

    return response;
  }

  /**
   * 取消请求
   * @param request - 取消请求对象
   * @returns 是否成功取消
   */
  private cancelRequest(request: CancellationRequest): boolean {
    const targetId = request.data.args[0];
    const controller = this.abortControllers[targetId];
    if (controller) {
      controller.abort();
      return true;
    }
    return false;
  }

  /**
   * 绑定Agent实例
   * @param agent - Agent实例
   */
  bind(agent: Agent): void {
    this.agent = agent;

    // 为每个事件名称注册监听器
    for (const eventName of agentEventNames) {
      this.agent.on(eventName, (event) => {
        // 发送事件通知
        this.tcpMessenger.send("agentEvent", { id: 0, data: event });
      });
    }
  }

  /**
   * 等待连接并开始监听
   */
  async listen(): Promise<void> {
    console.log("[TcpIO] Waiting for client connection...");
    await this.tcpMessenger.awaitConnection();
    console.log("[TcpIO] Client connected, ready to handle requests");

    // 设置进程信号处理
    ["SIGTERM", "SIGINT"].forEach((sig) => {
      process.on(sig, async () => {
        console.log(`[TcpIO] Received ${sig}, shutting down...`);
        if (this.agent && this.agent.getStatus() !== "finalized") {
          await this.agent.finalize();
        }
        this.tcpMessenger.close();
        process.exit(0);
      });
    });
  }

  /**
   * 关闭TCP连接
   */
  close(): void {
    this.tcpMessenger.close();
  }
}
