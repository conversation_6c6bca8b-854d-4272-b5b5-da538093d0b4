import { expect } from "chai";
import { processFillInTheLine } from "./processFillInTheLine";
import { documentContext } from "./testUtils";

describe("postprocess", () => {
  describe("processFillInTheLine", () => {
    const dummyContext = {
      ...documentContext`
      dummy║end;
      `,
      language: "plaintext",
    };
    const dummyContext2 = {
      ...documentContext`
      dummy║/,
      `,
      language: "plaintext",
    };

    it("case 1", () => {
      expect(processFillInTheLine()("inputend;\n", dummyContext)).to.eq("input");
    });
    it("case 2", () => {
      expect(processFillInTheLine()("inputendaaa\n", dummyContext)).to.eq("inputendaaa"); // trim the ending \n as well because we already know the suffix must have \n
    });
    it("case 3", () => {
      expect(processFillInTheLine()("inEend;\n", dummyContext)).to.eq("inE");
    });
    it("case 4", () => {
      expect(processFillInTheLine()("OR/,\\r", dummyContext2)).to.eq("OR");
    });
    it("case 5", () => {
      expect(processFillInTheLine()("OR/,\\r\\n ", dummyContext2)).to.eq("OR");
    });
    it("case 6", () => {
      expect(processFillInTheLine()("OR/,\r\n ", dummyContext2)).to.eq("OR");
    });
    it("case 7", () => {
      expect(processFillInTheLine()("OR/,\\n ", dummyContext2)).to.eq("OR");
    });
  });
});
