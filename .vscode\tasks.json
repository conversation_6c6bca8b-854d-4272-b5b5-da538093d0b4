{"version": "2.0.0", "tasks": [{"label": "Build Tabby Agent", "type": "npm", "script": "build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "Watch Build", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"label": "Start TCP Mode (Built)", "type": "shell", "command": "node", "args": ["dist/index.js", "--tcp", "--port", "3001"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"env": {"TABBY_DEVELOPMENT": "true", "TABBY_AGENT_TCP_MODE": "true", "TABBY_AGENT_TCP_PORT": "3001"}}, "dependsOn": "Build Tabby Agent"}, {"label": "Start TCP Mode (Source)", "type": "shell", "command": "npx", "args": ["ts-node", "src/cli.ts", "--tcp", "--port", "3001"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"env": {"TABBY_DEVELOPMENT": "true", "TABBY_AGENT_TCP_MODE": "true", "TABBY_AGENT_TCP_PORT": "3001"}}}, {"label": "Test TCP Client", "type": "shell", "command": "node", "args": ["test-tcp-client.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}, {"label": "Test Completion", "type": "shell", "command": "node", "args": ["test-completion.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}, {"label": "<PERSON><PERSON>", "type": "npm", "script": "lint", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Test", "type": "npm", "script": "test", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}