import net from "net";
import { v4 as uuidv4 } from "uuid";
import { TcpMessage } from "./TcpMessenger";

/**
 * TCP客户端类
 * 用于测试和调试TCP模式的tabby-agent
 */
export class TcpClient {
  private socket: net.Socket | null = null;
  private host: string;
  private port: number;
  private responseHandlers = new Map<string, (response: any) => void>();
  private _unfinishedLine: string | undefined = undefined;

  constructor(host: string = "127.0.0.1", port: number = 3000) {
    this.host = host;
    this.port = port;
  }

  /**
   * 连接到TCP服务器
   */
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket = new net.Socket();
      
      this.socket.connect(this.port, this.host, () => {
        console.log(`[TcpClient] Connected to ${this.host}:${this.port}`);
        resolve();
      });

      this.socket.on("data", (data: Buffer) => {
        this._handleData(data);
      });

      this.socket.on("error", (error) => {
        console.error("[TcpClient] Connection error:", error);
        reject(error);
      });

      this.socket.on("close", () => {
        console.log("[TcpClient] Connection closed");
        this.socket = null;
      });
    });
  }

  /**
   * 处理接收到的数据
   */
  private _handleData(data: Buffer) {
    const d = data.toString();
    const lines = d.split(/\r\n/).filter((line) => line.trim() !== "");
    if (lines.length === 0) {
      return;
    }

    if (this._unfinishedLine) {
      lines[0] = this._unfinishedLine + lines[0];
      this._unfinishedLine = undefined;
    }
    if (!d.endsWith("\r\n")) {
      this._unfinishedLine = lines.pop();
    }
    lines.forEach((line) => this._handleLine(line));
  }

  /**
   * 处理接收到的消息行
   */
  private _handleLine(line: string) {
    try {
      const message: TcpMessage = JSON.parse(line);
      console.log("[TcpClient] Received:", message);

      // 处理响应
      if (message.messageType === "agentResponse") {
        const handler = this.responseHandlers.get(message.messageId);
        if (handler) {
          handler(message.data);
          this.responseHandlers.delete(message.messageId);
        }
      } else if (message.messageType === "agentEvent") {
        console.log("[TcpClient] Agent Event:", message.data);
      }
    } catch (error) {
      console.error("[TcpClient] Error parsing message:", error);
    }
  }

  /**
   * 发送消息
   */
  private send(messageType: string, data: any, messageId?: string): string {
    if (!this.socket) {
      throw new Error("Not connected to server");
    }

    messageId = messageId || uuidv4();
    const message: TcpMessage = {
      messageType,
      data,
      messageId,
    };

    this.socket.write(JSON.stringify(message) + "\r\n");
    return messageId;
  }

  /**
   * 发送Agent请求
   */
  async sendAgentRequest(func: string, args: any[]): Promise<any> {
    const requestId = Math.floor(Math.random() * 1000000);
    const request = {
      id: requestId,
      data: {
        func,
        args,
      },
    };

    return new Promise((resolve, reject) => {
      const messageId = this.send("agentRequest", request);
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.responseHandlers.delete(messageId);
        reject(new Error("Request timeout"));
      }, 30000);

      this.responseHandlers.set(messageId, (response) => {
        clearTimeout(timeout);
        if (response && response.data !== null) {
          resolve(response.data);
        } else {
          reject(new Error("Request failed"));
        }
      });
    });
  }

  /**
   * 初始化Agent
   */
  async initialize(config?: any): Promise<boolean> {
    return this.sendAgentRequest("initialize", [{ config }]);
  }

  /**
   * 获取Agent状态
   */
  async getStatus(): Promise<string> {
    return this.sendAgentRequest("getStatus", []);
  }

  /**
   * 获取Agent配置
   */
  async getConfig(): Promise<any> {
    return this.sendAgentRequest("getConfig", []);
  }

  /**
   * 请求代码补全
   */
  async provideCompletions(request: any): Promise<any> {
    return this.sendAgentRequest("provideCompletions", [request, { signal: null }]);
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.end();
      this.socket = null;
    }
  }
}

/**
 * 简单的测试函数
 */
export async function testTcpClient() {
  const client = new TcpClient();
  
  try {
    console.log("Connecting to TCP server...");
    await client.connect();
    
    console.log("Initializing agent...");
    const initResult = await client.initialize();
    console.log("Init result:", initResult);
    
    console.log("Getting agent status...");
    const status = await client.getStatus();
    console.log("Status:", status);
    
    console.log("Getting agent config...");
    const config = await client.getConfig();
    console.log("Config keys:", Object.keys(config));
    
    // 测试代码补全
    console.log("Testing code completion...");
    const completionRequest = {
      id: "test-completion",
      filepath: "test.js",
      language: "javascript",
      text: "function hello() {\n  console.log('hello');\n  ",
      position: { line: 2, character: 2 },
      manually: true
    };
    
    const completion = await client.provideCompletions(completionRequest);
    console.log("Completion result:", completion);
    
  } catch (error) {
    console.error("Test failed:", error);
  } finally {
    client.disconnect();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testTcpClient();
}
