# ONNX 补全质量预测模块

## 概述

该模块为代码补全功能提供基于机器学习模型的高质量过滤机制。通过 ONNX 运行时进行推理，能有效过滤掉低质量的代码补全结果，提高整体补全质量。

## 主要组件

### OnnxPredictor.ts

TypeScript 实现的 ONNX 模型预测器，将 Rust 实现迁移到了 TypeScript。主要功能：

- 加载 ONNX 随机森林模型
- 从 token 概率数据提取特征
- 预测是否应接受当前补全结果

### cpl-rf-predictor.onnx

训练好的随机森林模型，用于预测补全质量。该模型接受 14 个特征作为输入，输出二分类结果：
- 0: 拒绝补全
- 1: 接受补全

## 工作原理

1. 当服务器返回补全结果时，模块从原始响应中提取 token 概率数据
2. 提取关键特征（均值、方差、波动性等）
3. 将特征输入 ONNX 模型进行推理
4. 根据模型预测结果决定是否接受补全

## 集成方式

该模块已集成到 TabbyAgent 中，在处理补全响应的过程中自动触发预测与过滤。

## 特征说明

模型使用的 14 个特征：

1. mean - token 概率均值
2. min - 最小概率值
3. max - 最大概率值
4. stdDev - 标准差
5. q1 - 第一四分位数 (25%)
6. tokenCount - token 数量
7. firstProb - 第一个 token 的概率
8. lastProb - 最后一个 token 的概率
9. maxDrop - 最大概率下降幅度
10. volatility - 概率波动性
11. endQuality - 结尾部分质量
12. log1pPrefixLen - 前缀长度对数 (ln(prefix_len + 1))
13. log1pSuffixLen - 后缀长度对数 (ln(suffix_len + 1))
14. contextRatio - 前缀占总上下文的比例 