import type TreeSitterParser from "web-tree-sitter";
import { getParser, languagesConfigs } from "../syntax/parser";
import { typeList } from "../syntax/typeList";
import { CompletionContext } from "../CompletionContext";
import { PostprocessFilter } from "./base";
import {logger} from "../logger";
import { splitLines, joinLines } from "../utils";

/** 支持语法分析的语言列表 */
export const supportedLanguages = Object.keys(languagesConfigs);

/** 解析器缓存，避免重复初始化 */
const parserCache = new Map<string, Promise<TreeSitterParser>>();

/**
 * 获取带缓存的解析器实例
 * 
 * @param languageConfig - 语言配置名称
 * @returns 解析器实例
 */
async function getCachedParser(languageConfig: string): Promise<TreeSitterParser> {
  if (!parserCache.has(languageConfig)) {
    parserCache.set(languageConfig, getParser(languageConfig));
  }
  return parserCache.get(languageConfig)!;
}

// public void fibonacci() {
//   /cursor/System.out.println(1);
// if (true) {
//   System.out.println(1);
// }
// public void fib123() {
//   return null
//
// }
// line begin is 0, the first line start
// line end is the second line ending "\n", which is 50
// eslint-disable-next-line @typescript-eslint/no-unused-vars
// @ts-ignore
function findLineBegin(text: string, position: number): number {
  let lastNonBlankCharPos = position - 1;
  while (lastNonBlankCharPos >= 0 && text[lastNonBlankCharPos]?.match(/\s/)) {
    lastNonBlankCharPos--;
  }
  if (lastNonBlankCharPos < 0) {
    return 0; // 说明光标之前的所有字符都是空白字符，函数返回 0，表示光标所在行从文本的开头开始。
  }
  const lineBegin = text.lastIndexOf("\n", lastNonBlankCharPos); //second param is for right to left search, finding the first \n char before non blank char
  if (lineBegin < 0) {
    return 0;
  }
  const line = text.slice(lineBegin + 1, position);
  const indentation = line.search(/\S/);
  return lineBegin + 1 + indentation;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// @ts-ignore
function findLineEnd(text: string, position: number): number {
  let firstNonBlankCharPos = position;
  while (firstNonBlankCharPos < text.length && text[firstNonBlankCharPos]?.match(/\s/)) {
    firstNonBlankCharPos++;
  }
  if (firstNonBlankCharPos >= text.length) {
    return text.length;
  }
  const lineEnd = text.indexOf("\n", firstNonBlankCharPos); //start position from left to right
  if (lineEnd < 0) {
    return text.length;
  }
  return lineEnd;
}
// Why Might the Function Return the Original Node?
//   No Matching Ancestor: If none of the ancestor nodes (including the node itself) match any type in typeList, the function will return the original node.
//   Empty typeList: If typeList is empty or contains empty arrays, the function won't find any match and will return the original node.

/**
 * 查找语法作用域节点
 * 从当前节点向上查找匹配指定类型的最近祖先节点
 * 
 * @param node - 起始语法节点
 * @param typeList - 目标节点类型列表
 * @returns 找到的作用域节点或原始节点
 */
function findScope(node: TreeSitterParser.SyntaxNode, typeList: string[][]): TreeSitterParser.SyntaxNode {
  for (const types of typeList) {
    let scope: TreeSitterParser.SyntaxNode | null = node;
    while (scope) {
      if (types.includes(scope.type)) {
        return scope;
      }
      scope = scope.parent;
    }
  }
  return node;
}

/**
 * 创建一个基于语法分析的范围限制过滤器
 * 使用Tree-sitter解析器分析代码结构来限制补全范围
 * 
 * @returns 基于语法分析限制范围的过滤器函数
 */
export function limitScopeBySyntax(): PostprocessFilter {
  return async (input: string, context: CompletionContext) => {
    const funcStartTime = Date.now();
    const { position, language, prefix, suffix } = context;
    
    // 快速路径：不支持的语言直接返回
    if (!supportedLanguages.includes(language)) {
      logger.debug(`[Syntax] Unsupported language ${language}, skipping (${Date.now() - funcStartTime}ms)`);
      return input;
    }
    
    // 快速路径：小输入快速返回
    if (input.length <= 5) {
      logger.debug(`[Syntax] Input too small (${input.length} chars), skipping (${Date.now() - funcStartTime}ms)`);
      return input;
    }
    
    const parserStartTime = Date.now();
    const languageConfig = languagesConfigs[language]!;
    
    // 使用缓存的解析器
    const parser = await getCachedParser(languageConfig);
    const parserEndTime = Date.now();
    logger.debug(`[Syntax] Parser initialization took ${parserEndTime - parserStartTime}ms`);
    
    const parseStartTime = Date.now();
    const updatedText = prefix + input + suffix;1
    const updatedTree = parser.parse(updatedText);
    const parseEndTime = Date.now();
    logger.debug(`[Syntax] Tree parsing took ${parseEndTime - parseStartTime}ms`);
    
    const lineCalcStartTime = Date.now();
    const lineBegin = findLineBegin(updatedText, position);
    const lineEnd = findLineEnd(updatedText, position);
    const lineCalcEndTime = Date.now();
    logger.debug(`[Syntax] Line calculation took ${lineCalcEndTime - lineCalcStartTime}ms`);

    const scopeStartTime = Date.now();
    const minimumParentNode = updatedTree.rootNode.namedDescendantForIndex(lineBegin, lineEnd);
    const scope = findScope(
      // The start position of the returned node will be less than or equal to lineBegin.
      // The end position of the returned node will be greater than or equal to lineEnd.
      minimumParentNode, //Smallest Named Node: The method will return the deepest (most specific) node in the AST that spans the range from lineBegin to lineEnd.
      typeList[languageConfig] ?? [],
    );
    const scopeEndTime = Date.now();
    logger.debug(`[Syntax] Scope finding took ${scopeEndTime - scopeStartTime}ms, scope type: ${scope.type}, start: ${scope.startIndex}, end: ${scope.endIndex}`);

    if (scope.endIndex < position + input.length) {
      const scopeCheckTime = Date.now();
      logger.debug(`[Syntax] Removing content out of syntax scope (${scopeCheckTime - scopeEndTime}ms)`);
      return input.slice(0, scope.endIndex - position);
    }
    
    // 检查是否可以通过删除几行来修复语法错误
    // const errorCheckStartTime = Date.now();
    let index = 4; // most weird behavior happens in the last three lines
    const filteredInput = splitLines(input);
    let lastTreeParseTime = 0;
    
    while (index > 0 && filteredInput.length > 0) {
      const lineParseStartTime = Date.now();
      const text = prefix + joinLines(filteredInput) + suffix;
      const tree = parser.parse(text);
      lastTreeParseTime = Date.now() - lineParseStartTime;
      
      const lineBegin = findLineBegin(text, position);
      const lineEnd = findLineEnd(text, position);
      const node = tree.rootNode.namedDescendantForIndex(lineBegin, lineEnd);
      
      if (node.hasError()) {
        filteredInput.pop() // delete single line from the end
        logger.debug(`[Syntax] Error found, removing line. Lines left: ${filteredInput.length}, parse time: ${lastTreeParseTime}ms`);
      }
      else {
        const finalTime = Date.now();
        logger.debug(`[Syntax] No errors found, returning filtered input after ${index} iterations. Total: ${finalTime - funcStartTime}ms`);
        return joinLines(filteredInput);
      }
      index = index - 1;
    }
    
    const finalTime = Date.now();
    logger.debug(`[Syntax] Completed syntax checking in ${finalTime - funcStartTime}ms. Tree parsing took: ${lastTreeParseTime}ms per iteration`);
    return input;
  };
}
