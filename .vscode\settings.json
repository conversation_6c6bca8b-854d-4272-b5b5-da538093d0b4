{"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": false, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/.git/**": true}, "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "terminal.integrated.fontSize": 14, "terminal.integrated.lineHeight": 1.2, "eslint.workingDirectories": ["."], "eslint.validate": ["javascript", "typescript"], "typescript.preferences.quoteStyle": "double", "javascript.preferences.quoteStyle": "double", "editor.rulers": [80, 120], "editor.wordWrap": "bounded", "editor.wordWrapColumn": 120, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "files.trimFinalNewlines": true, "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["tsconfig.json", "tsconfig.*.json"], "url": "https://json.schemastore.org/tsconfig.json"}], "emmet.includeLanguages": {"typescript": "javascript"}, "git.ignoreLimitWarning": true, "extensions.ignoreRecommendations": false, "workbench.editor.enablePreview": false, "workbench.editor.enablePreviewFromQuickOpen": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false}