import { expect } from "chai";
import { inline ,documentContext} from "./testUtils";
import { dropUnfinishedMulti } from "./dropUnfinishedMulti";

describe("postprocess", () => {
  describe("dropUnfinishedMulti", () => {
    it("trim the unfinished last line", () => {
      const context = {
        ...documentContext`
        train_pipeline = [
          "help": "Path to folder or file with trai║
          " point to \`toy_data_instruct\` instead. If the path is a folder, for each minibatch"
          " all samples will come from one file in the folder. You can use this to ensure"
          " in-batch negatives are very difficult."
        };
        `,
        language: "python",
      };
      const completion = inline`
            ├throw new Error("check not passed");
             throw new Error("┤
      `;
      expect(dropUnfinishedMulti()(completion,context)).to.eq("throw new Error(\"check not passed\");\n")
    });
  });
});
