import { PostprocessFilter } from "./base";
import { splitLines, isBlank } from "../utils";

/**
 * 重复模式检测规则
 * 用于识别行尾的重复内容
 */
const repetitionTests = [
  /(.{3,}?)\1{5,}$/g, // 匹配3个或更多字符重复5次以上
  /(.{10,}?)\1{3,}$/g, // 匹配10个或更多字符重复3次以上
];

/**
 * 创建一个移除行尾重复内容的过滤器
 * 检测并移除最后一个非空行中的重复模式
 * 
 * @returns 移除行尾重复的过滤器函数
 */
export function removeLineEndsWithRepetition(): PostprocessFilter {
  return (input: string) => {
    // 仅检查最后一个非空行
    const inputLines = splitLines(input);
    let index = inputLines.length - 1;
    while (index >= 0 && isBlank(inputLines[index]!)) {
      index--;
    }
    
    // 空输入直接返回
    if (index < 0) return input;
    
    // 检查是否匹配重复模式
    for (const test of repetitionTests) {
      const match = inputLines[index]!.match(test);
      if (match) {
        // 如果找到重复，移除该行
        if (index < 1) return null;
        return inputLines.slice(0, index).join("").trimEnd();
      }
    }
    
    // 未找到重复
    return input;
  };
}
