/**
 * 主入口文件
 * 确保按照正确的顺序加载模块，先初始化环境变量，再初始化日志
 */

// 首先导入环境配置，确保全局变量初始化
import './env';

// 然后导入日志模块
import { logger } from './logger';

// 必须在所有ONNX相关导入之前设置绑定路径
import { setupOnnxBindingResolver } from './utils/onnx-resolver';

// 设置ONNX绑定路径
setupOnnxBindingResolver();


// 导出所有需要的模块
export * from './Agent';
export * from './AgentConfig';
export * from './TabbyAgent';
export * from './StdIO';
export * from './utils';
export { logger };

// 在初始化完成时输出日志
logger.info('初始化完成：环境变量和日志系统已加载');

/**
 * Agent模块导出文件
 * 集中导出所有公共API
 */

/** 导出主要Agent类 */
export { TabbyAgent } from "./TabbyAgent";

/** 导出Agent相关类型和接口 */
export {
    /** Agent基础类型 */
    Agent,
    /** Agent状态枚举 */
    AgentStatus,
    /** Agent功能接口 */
    AgentFunction,
    /** Agent事件类型 */
    AgentEvent,
    /** Agent事件发射器接口 */
    AgentEventEmitter,
    /** 状态变更事件 */
    StatusChangedEvent,
    /** 配置更新事件 */
    ConfigUpdatedEvent,
    /** 客户端属性类型 */
    ClientProperties,
    /** Agent初始化选项 */
    AgentInitOptions,
    /** 补全请求类型 */
    CompletionRequest,
    /** 补全响应类型 */
    CompletionResponse,
    /** 中止信号选项 */
    AbortSignalOption,
    /** Agent事件名称列表 */
    agentEventNames,
} from "./Agent";

/** 导出Agent配置相关类型 */
export { AgentConfig, PartialAgentConfig } from "./AgentConfig";


